{% for req in recent_requests %} <tr><td><span>{{ req.request_number }}</span></td><td><div>{{ req.request_title }}</div></td><td><div><div><div>{{ req..full_name }}</div><div>{{ req..email }}</div></div></div></td><td> {% if req.status.value == 'pending' %} <span> قيد المراجعة </span> {% elif req.status.value == 'in_' %} <span> قيد التنفيذ </span> {% elif req.status.value == 'completed' %} <span> مكتمل </span> {% elif req.status.value == 'rejected' %} <span> مرفوض </span> {% endif %} </td><td><div>{{ req.created_at.strftime('%Y-%%d') }}</div><div>{{ req.created_at.strftime('%H:%M') }}</div></td><td><div><a href="/requests/{{ req.id }}"> عرض </a><div><button type="button"> إجراءات </button><ul><li><form method="post" action="//requests/{{ req.id }}/update-status"><i><button type="submit"> قيد التنفيذ </button></form></li><li><form method="post" action="//requests/{{ req.id }}/update-status"><i><button type="submit"> مكتمل </button></form></li><li><form method="post" action="//requests/{{ req.id }}/update-status"><i><button type="submit"> مرفوض </button></form></li></ul></div></div></td></tr> {% endfor %} {% if recent_requests %} <!-- Load More Button Row --><tr id><td="6"><button hx-get="//api/requests/loa?skip={{ next_skip }}" hx-target="#" hx-swap="beforeend" hx-indicator="#" onclick="this.closest('tr').remove()"> تحميل المزيد </button><div id><div role="status"><span>جاري التحميل...</span></div></div></td></tr> {% endif %} 