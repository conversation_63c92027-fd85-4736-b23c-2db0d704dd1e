<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجل أنشطة {{ target_user.full_name or target_user.username }} - لوحة التحكم</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Modern Arabic font stack */
        body {
            font-family: 'Segoe UI', 'Tahoma', 'Geneva', 'Verdana', sans-serif, 'Arabic UI Text', 'Geeza Pro', 'Traditional Arabic', 'Simplified Arabic';
        }

        /* Enhanced profile avatar */
        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background-size: cover;
            background-position: center;
            background-color: #f3f4f6;
            border: 4px solid #ffffff;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            flex-shrink: 0;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .profile-avatar:hover {
            transform: scale(1.05);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.18);
        }

        /* Enhanced activity badges */
        .activity-badge {
            display: inline-flex;
            align-items: center;
            padding: 6px 14px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.3px;
            transition: all 0.2s ease;
        }

        .activity-badge.login {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
        }

        .activity-badge.request_created {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        .activity-badge.request_updated {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
        }

        .activity-badge.request_completed {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
        }

        .activity-badge.request_rejected {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
        }

        .activity-badge.profile_updated {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
            box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
        }

        .activity-badge.avatar_uploaded {
            background: linear-gradient(135deg, #06b6d4, #0891b2);
            color: white;
            box-shadow: 0 2px 8px rgba(6, 182, 212, 0.3);
        }

        .activity-badge.file_uploaded {
            background: linear-gradient(135deg, #84cc16, #65a30d);
            color: white;
            box-shadow: 0 2px 8px rgba(132, 204, 22, 0.3);
        }

        /* Role badges */
        .role-badge {
            display: inline-flex;
            align-items: center;
            padding: 6px 14px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        .role-badge.admin {
            background-color: #fef3c7;
            color: #92400e;
            border: 2px solid #fbbf24;
        }

        .role-badge.manager {
            background-color: #dbeafe;
            color: #1e40af;
            border: 2px solid #3b82f6;
        }

        .role-badge.user {
            background-color: #f3e8ff;
            color: #7c3aed;
            border: 2px solid #8b5cf6;
        }

        /* Enhanced buttons */
        .primary-btn {
            background: linear-gradient(135deg, #06b6d4, #0891b2);
            color: white;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3);
        }

        .primary-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(6, 182, 212, 0.4);
        }

        .secondary-btn {
            background: linear-gradient(135deg, #6b7280, #4b5563);
            color: white;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);
        }

        .secondary-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(107, 114, 128, 0.4);
        }

        /* Enhanced form styling */
        .form-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }

        .form-section {
            padding: 2rem;
            border-bottom: 1px solid #f1f5f9;
        }

        .form-section:last-child {
            border-bottom: none;
        }

        /* Header styling */
        .page-header {
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            color: white;
            border-radius: 20px;
            margin-bottom: 2rem;
            padding: 2.5rem;
            box-shadow: 0 15px 35px rgba(6, 182, 212, 0.3);
        }

        /* Stats cards */
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }

        /* Timeline styling */
        .timeline-container {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid #e5e7eb;
        }

        .timeline-item {
            position: relative;
            padding: 1.5rem;
            border-bottom: 1px solid #f1f5f9;
            transition: background-color 0.2s ease;
        }

        .timeline-item:hover {
            background-color: #f8fafc;
        }

        .timeline-item:last-child {
            border-bottom: none;
        }

        .timeline-connector {
            position: absolute;
            top: 2rem;
            right: 2rem;
            width: 2px;
            height: calc(100% - 1rem);
            background: linear-gradient(to bottom, #e5e7eb, #f3f4f6);
        }

        .timeline-icon {
            position: relative;
            z-index: 10;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 3px solid white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* Success/Error messages */
        .alert {
            padding: 16px 20px;
            border-radius: 12px;
            margin-bottom: 1.5rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .alert-success {
            background-color: #ecfdf5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .alert-error {
            background-color: #fef2f2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }

        .alert-info {
            background-color: #eff6ff;
            color: #1e40af;
            border: 1px solid #bfdbfe;
        }

        /* Loading animation */
        .loading-shimmer {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            .form-section {
                padding: 1.5rem;
            }

            .page-header {
                padding: 2rem;
            }

            .stat-card {
                padding: 1rem;
            }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Enhanced Header Section -->
        <div class="page-header">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-6 space-x-reverse">
                    <div class="profile-avatar" style="background-image: url('{{ target_user_avatar_url }}');"></div>
                    <div>
                        <h1 class="text-4xl font-bold mb-2">سجل أنشطة {{ target_user.full_name or target_user.username }}</h1>
                        <p class="text-blue-100 text-lg mb-3">تتبع ومراقبة جميع أنشطة المستخدم</p>
                        <div class="flex items-center space-x-4 space-x-reverse">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <i class="fas fa-envelope text-blue-300"></i>
                                <span class="text-sm">{{ target_user.email }}</span>
                            </div>
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <i class="fas fa-user-tag text-blue-300"></i>
                                <span class="role-badge {{ target_user.role.value }}">
                                    {% if target_user.role.value == 'admin' %}
                                        <i class="fas fa-crown mr-1"></i>
                                        مدير النظام
                                    {% elif target_user.role.value == 'manager' %}
                                        <i class="fas fa-user-tie mr-1"></i>
                                        مدير المشاريع
                                    {% else %}
                                        <i class="fas fa-user mr-1"></i>
                                        عضو الفريق
                                    {% endif %}
                                </span>
                            </div>
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <i class="fas fa-chart-line text-blue-300"></i>
                                <span class="activity-badge {{ activity_stats.activity_level|lower }}">
                                    <i class="fas fa-circle mr-1 text-xs"></i>
                                    {{ activity_stats.activity_level }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex space-x-3 space-x-reverse">
                    <a href="/admin/users/{{ target_user.id }}/requests" class="inline-flex items-center px-4 py-2 bg-white bg-opacity-20 text-white rounded-lg hover:bg-opacity-30 transition-all duration-200">
                        <i class="fas fa-file-alt ml-2"></i>
                        طلبات المستخدم
                    </a>
                    <a href="/admin/users/{{ target_user.id }}/edit" class="inline-flex items-center px-4 py-2 bg-white bg-opacity-20 text-white rounded-lg hover:bg-opacity-30 transition-all duration-200">
                        <i class="fas fa-edit ml-2"></i>
                        تعديل المستخدم
                    </a>
                    <a href="/admin/users/table" class="inline-flex items-center px-4 py-2 bg-white bg-opacity-20 text-white rounded-lg hover:bg-opacity-30 transition-all duration-200">
                        <i class="fas fa-arrow-right ml-2"></i>
                        العودة للمستخدمين
                    </a>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Activities -->
            <div class="stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">إجمالي الأنشطة</p>
                        <p class="text-3xl font-bold text-blue-600">{{ activity_stats.total_activities }}</p>
                        <p class="text-xs text-gray-500 mt-1">جميع الأنشطة المسجلة</p>
                    </div>
                    <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-chart-line text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Daily Activities -->
            <div class="stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">أنشطة اليوم</p>
                        <p class="text-3xl font-bold text-green-600">{{ activity_stats.daily_activities }}</p>
                        <p class="text-xs text-gray-500 mt-1">آخر 24 ساعة</p>
                    </div>
                    <div class="w-14 h-14 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-calendar-day text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Weekly Activities -->
            <div class="stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">أنشطة الأسبوع</p>
                        <p class="text-3xl font-bold text-yellow-600">{{ activity_stats.weekly_activities }}</p>
                        <p class="text-xs text-gray-500 mt-1">آخر 7 أيام</p>
                    </div>
                    <div class="w-14 h-14 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-calendar-week text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Monthly Activities -->
            <div class="stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">آخر 30 يوم</p>
                        <p class="text-3xl font-bold text-purple-600">{{ activity_stats.recent_activities }}</p>
                        <p class="text-xs text-gray-500 mt-1">الشهر الماضي</p>
                    </div>
                    <div class="w-14 h-14 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-calendar-alt text-white text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Filters Section -->
        <div class="form-container mb-8">
            <div class="form-section">
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-filter text-white text-lg"></i>
                        </div>
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900">فلترة الأنشطة</h2>
                            <p class="text-gray-600">ابحث وصفي الأنشطة حسب النوع والتاريخ</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse text-sm text-gray-500">
                        <i class="fas fa-info-circle"></i>
                        <span>{{ activities|length }} نشاط</span>
                    </div>
                </div>

                <form method="GET" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Activity Type Filter -->
                        <div>
                            <label for="activity_type" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-tags text-gray-400 mr-1"></i>
                                نوع النشاط
                            </label>
                            <div class="relative">
                                <select id="activity_type" name="activity_type" class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 pl-10">
                                    <option value="">جميع الأنشطة</option>
                                    <option value="login" {% if current_activity_type == 'login' %}selected{% endif %}>تسجيل دخول</option>
                                    <option value="request_created" {% if current_activity_type == 'request_created' %}selected{% endif %}>إنشاء طلب</option>
                                    <option value="request_updated" {% if current_activity_type == 'request_updated' %}selected{% endif %}>تحديث طلب</option>
                                    <option value="request_completed" {% if current_activity_type == 'request_completed' %}selected{% endif %}>إكمال طلب</option>
                                    <option value="request_rejected" {% if current_activity_type == 'request_rejected' %}selected{% endif %}>رفض طلب</option>
                                    <option value="profile_updated" {% if current_activity_type == 'profile_updated' %}selected{% endif %}>تحديث الملف الشخصي</option>
                                    <option value="avatar_uploaded" {% if current_activity_type == 'avatar_uploaded' %}selected{% endif %}>رفع صورة شخصية</option>
                                    <option value="file_uploaded" {% if current_activity_type == 'file_uploaded' %}selected{% endif %}>رفع ملفات</option>
                                </select>
                                <i class="fas fa-tags absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>
                        </div>

                        <!-- Date From -->
                        <div>
                            <label for="date_from" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-calendar-alt text-gray-400 mr-1"></i>
                                من تاريخ
                            </label>
                            <div class="relative">
                                <input type="date"
                                       id="date_from"
                                       name="date_from"
                                       value="{{ current_date_from or '' }}"
                                       class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 pl-10">
                                <i class="fas fa-calendar-alt absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>
                        </div>

                        <!-- Date To -->
                        <div>
                            <label for="date_to" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-calendar-check text-gray-400 mr-1"></i>
                                إلى تاريخ
                            </label>
                            <div class="relative">
                                <input type="date"
                                       id="date_to"
                                       name="date_to"
                                       value="{{ current_date_to or '' }}"
                                       class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 pl-10">
                                <i class="fas fa-calendar-check absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-between items-center pt-4 border-t border-gray-200">
                        <div class="flex space-x-3 space-x-reverse">
                            <button type="submit" class="primary-btn">
                                <i class="fas fa-search"></i>
                                بحث
                            </button>
                            <a href="/admin/users/{{ target_user.id }}/activities" class="secondary-btn">
                                <i class="fas fa-times"></i>
                                مسح الفلاتر
                            </a>
                        </div>

                        <!-- Export Options -->
                        <div class="relative">
                            <button type="button" id="exportBtn" class="inline-flex items-center px-4 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors">
                                <i class="fas fa-download mr-2"></i>
                                تصدير البيانات
                            </button>
                            <div id="exportMenu" class="hidden absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                                <a href="/admin/users/{{ target_user.id }}/activities/export?format=csv{% if current_activity_type %}&activity_type={{ current_activity_type }}{% endif %}{% if current_date_from %}&date_from={{ current_date_from }}{% endif %}{% if current_date_to %}&date_to={{ current_date_to }}{% endif %}"
                                   class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-t-lg">
                                    <i class="fas fa-file-csv mr-2"></i>
                                    تصدير CSV
                                </a>
                                <a href="/admin/users/{{ target_user.id }}/activities/export?format=json{% if current_activity_type %}&activity_type={{ current_activity_type }}{% endif %}{% if current_date_from %}&date_from={{ current_date_from }}{% endif %}{% if current_date_to %}&date_to={{ current_date_to }}{% endif %}"
                                   class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-b-lg">
                                    <i class="fas fa-file-code mr-2"></i>
                                    تصدير JSON
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Filter Results Info -->
                    {% if current_activity_type or current_date_from or current_date_to %}
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div class="flex items-center space-x-3 space-x-reverse">
                            <i class="fas fa-info-circle text-blue-500"></i>
                            <div>
                                <p class="text-sm font-medium text-blue-800">نتائج الفلترة:</p>
                                <div class="text-sm text-blue-600">
                                    {% if current_activity_type %}
                                    <span>النوع: {{ current_activity_type }}</span>
                                    {% endif %}
                                    {% if current_date_from %}
                                    <span>{% if current_activity_type %} • {% endif %}من: {{ current_date_from }}</span>
                                    {% endif %}
                                    {% if current_date_to %}
                                    <span>{% if current_activity_type or current_date_from %} • {% endif %}إلى: {{ current_date_to }}</span>
                                    {% endif %}
                                    <span> • {{ activities|length }} نشاط</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </form>
            </div>
        </div>

        <!-- Enhanced Activities Timeline -->
        {% if activities %}
        <div class="timeline-container">
            <div class="form-section border-b">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-history text-white text-lg"></i>
                        </div>
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900">سجل الأنشطة</h2>
                            <p class="text-gray-600">تسلسل زمني لجميع أنشطة المستخدم</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse text-sm text-gray-500">
                        <i class="fas fa-clock"></i>
                        <span>{{ activities|length }} نشاط</span>
                    </div>
                </div>
            </div>

            <div class="relative">
                <!-- Timeline Line -->
                <div class="absolute right-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-blue-200 via-purple-200 to-pink-200"></div>
                {% for activity in activities %}
                <div class="timeline-item">
                    <div class="flex items-start space-x-4 space-x-reverse">
                        <!-- Enhanced Activity Icon -->
                        <div class="timeline-icon bg-gradient-to-br
                            {% if activity.type == 'login' %}from-green-500 to-green-600
                            {% elif activity.type == 'request_created' %}from-blue-500 to-blue-600
                            {% elif activity.type == 'request_updated' %}from-yellow-500 to-yellow-600
                            {% elif activity.type == 'request_completed' %}from-green-500 to-green-600
                            {% elif activity.type == 'request_rejected' %}from-red-500 to-red-600
                            {% elif activity.type == 'profile_updated' %}from-purple-500 to-purple-600
                            {% elif activity.type == 'avatar_uploaded' %}from-cyan-500 to-cyan-600
                            {% elif activity.type == 'file_uploaded' %}from-lime-500 to-lime-600
                            {% else %}from-gray-500 to-gray-600
                            {% endif %}">
                            <i class="
                                {% if activity.type == 'login' %}fas fa-sign-in-alt
                                {% elif activity.type == 'request_created' %}fas fa-plus-circle
                                {% elif activity.type == 'request_updated' %}fas fa-edit
                                {% elif activity.type == 'request_completed' %}fas fa-check-circle
                                {% elif activity.type == 'request_rejected' %}fas fa-times-circle
                                {% elif activity.type == 'profile_updated' %}fas fa-user-edit
                                {% elif activity.type == 'avatar_uploaded' %}fas fa-image
                                {% elif activity.type == 'file_uploaded' %}fas fa-file-upload
                                {% else %}fas fa-circle
                                {% endif %} text-white"></i>
                        </div>

                        <!-- Enhanced Activity Content -->
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between mb-2">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">{{ activity.title }}</h3>
                                    <p class="text-gray-600">{{ activity.description }}</p>
                                </div>
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    <span class="activity-badge {{ activity.type }}">
                                        <i class="fas fa-tag mr-1"></i>
                                        {% if activity.type == 'login' %}تسجيل دخول
                                        {% elif activity.type == 'request_created' %}إنشاء طلب
                                        {% elif activity.type == 'request_updated' %}تحديث طلب
                                        {% elif activity.type == 'request_completed' %}إكمال طلب
                                        {% elif activity.type == 'request_rejected' %}رفض طلب
                                        {% elif activity.type == 'profile_updated' %}تحديث الملف
                                        {% elif activity.type == 'avatar_uploaded' %}رفع صورة
                                        {% elif activity.type == 'file_uploaded' %}رفع ملفات
                                        {% else %}{{ activity.type }}
                                        {% endif %}
                                    </span>
                                </div>
                            </div>

                            <!-- Enhanced Activity Details -->
                            {% if activity.details %}
                            <div class="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                                {% if activity.type in ['request_created', 'request_updated', 'request_completed', 'request_rejected'] %}
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <i class="fas fa-hashtag text-gray-400"></i>
                                        <span class="text-sm font-medium text-gray-700">رقم الطلب:</span>
                                        <code class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">{{ activity.details.request_number }}</code>
                                    </div>
                                    {% if activity.details.company_name %}
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <i class="fas fa-building text-gray-400"></i>
                                        <span class="text-sm font-medium text-gray-700">الشركة:</span>
                                        <span class="text-sm text-gray-900">{{ activity.details.company_name }}</span>
                                    </div>
                                    {% endif %}
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <i class="fas fa-info-circle text-gray-400"></i>
                                        <span class="text-sm font-medium text-gray-700">الحالة:</span>
                                        <span class="activity-badge {{ activity.details.status }}">
                                            {% if activity.details.status == 'pending' %}قيد الانتظار
                                            {% elif activity.details.status == 'in_progress' %}قيد المعالجة
                                            {% elif activity.details.status == 'completed' %}مكتمل
                                            {% elif activity.details.status == 'rejected' %}مرفوض
                                            {% else %}{{ activity.details.status }}
                                            {% endif %}
                                        </span>
                                    </div>
                                </div>
                                {% elif activity.type == 'file_uploaded' %}
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <i class="fas fa-file text-gray-400"></i>
                                        <span class="text-sm font-medium text-gray-700">عدد الملفات:</span>
                                        <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm font-semibold">{{ activity.details.file_count }}</span>
                                    </div>
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <i class="fas fa-hashtag text-gray-400"></i>
                                        <span class="text-sm font-medium text-gray-700">رقم الطلب:</span>
                                        <code class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">{{ activity.details.request_number }}</code>
                                    </div>
                                </div>
                                {% elif activity.type == 'avatar_uploaded' %}
                                <div class="flex items-center space-x-3 space-x-reverse">
                                    <img src="{{ activity.details.avatar_url }}" alt="Avatar" class="w-12 h-12 rounded-full border-2 border-white shadow-md">
                                    <div>
                                        <p class="text-sm font-medium text-gray-700">تم تحديث الصورة الشخصية</p>
                                        <p class="text-xs text-gray-500">صورة جديدة عالية الجودة</p>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                            {% endif %}

                            <!-- Enhanced Timestamp -->
                            <div class="mt-4 flex items-center justify-between">
                                <div class="flex items-center space-x-2 space-x-reverse text-sm text-gray-500">
                                    <i class="fas fa-clock"></i>
                                    <time datetime="{{ activity.timestamp.isoformat() }}">
                                        {{ activity.timestamp.strftime('%Y-%m-%d') }} في {{ activity.timestamp.strftime('%H:%M') }}
                                    </time>
                                </div>
                                <div class="text-xs text-gray-400">
                                    {% set time_diff = (now() - activity.timestamp).total_seconds() %}
                                    {% if time_diff < 3600 %}
                                        منذ {{ (time_diff / 60)|int }} دقيقة
                                    {% elif time_diff < 86400 %}
                                        منذ {{ (time_diff / 3600)|int }} ساعة
                                    {% else %}
                                        منذ {{ (time_diff / 86400)|int }} يوم
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Timeline Connector -->
                    {% if not loop.last %}
                    <div class="timeline-connector"></div>
                    {% endif %}
                </div>
                            </div>
                        </div>
                    </li>
                    {% endfor %}
            </div>
        </div>

        {% else %}
        <!-- Empty State -->
        <div class="form-container">
            <div class="form-section text-center py-12">
                <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-history text-4xl text-gray-400"></i>
                </div>

                <h3 class="text-2xl font-bold text-gray-900 mb-4">
                    {% if current_activity_type or current_date_from or current_date_to %}
                        لا توجد أنشطة تطابق البحث
                    {% else %}
                        لا توجد أنشطة لهذا المستخدم
                    {% endif %}
                </h3>

                <p class="text-gray-600 mb-8 max-w-md mx-auto">
                    {% if current_activity_type or current_date_from or current_date_to %}
                        لا توجد أنشطة تطابق معايير البحث المحددة. جرب تعديل الفلاتر أو البحث بتواريخ مختلفة.
                    {% else %}
                        لم يقم {{ target_user.full_name or target_user.username }} بأي أنشطة حتى الآن.
                    {% endif %}
                </p>

                <div class="flex justify-center space-x-4 space-x-reverse">
                    {% if current_activity_type or current_date_from or current_date_to %}
                    <a href="/admin/users/{{ target_user.id }}/activities" class="secondary-btn">
                        <i class="fas fa-list"></i>
                        عرض جميع الأنشطة
                    </a>
                    {% endif %}

                    <a href="/admin/users/{{ target_user.id }}/edit" class="primary-btn">
                        <i class="fas fa-user-edit"></i>
                        تعديل المستخدم
                    </a>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Export menu toggle
            const exportBtn = document.getElementById('exportBtn');
            const exportMenu = document.getElementById('exportMenu');

            if (exportBtn && exportMenu) {
                exportBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    exportMenu.classList.toggle('hidden');
                });

                // Close export menu when clicking outside
                document.addEventListener('click', function() {
                    exportMenu.classList.add('hidden');
                });

                exportMenu.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            }

            // Timeline animations
            const timelineItems = document.querySelectorAll('.timeline-item');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });

            timelineItems.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(20px)';
                item.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;
                observer.observe(item);
            });

            // Auto-refresh activities every 30 seconds
            setInterval(() => {
                const currentUrl = new URL(window.location);
                fetch(currentUrl.href, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.text())
                .then(html => {
                    // Update activity count in header
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const newCount = doc.querySelector('.timeline-container .form-section span');
                    const currentCount = document.querySelector('.timeline-container .form-section span');

                    if (newCount && currentCount && newCount.textContent !== currentCount.textContent) {
                        showAlert('تم تحديث الأنشطة - توجد أنشطة جديدة', 'info');
                    }
                })
                .catch(error => {
                    console.log('Auto-refresh failed:', error);
                });
            }, 30000);
        });

        // Alert function
        function showAlert(message, type = 'info') {
            // Remove existing alerts
            const existingAlerts = document.querySelectorAll('.alert');
            existingAlerts.forEach(alert => alert.remove());

            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;

            const icon = type === 'success' ? 'fa-check-circle' :
                        type === 'error' ? 'fa-exclamation-triangle' : 'fa-info-circle';

            alertDiv.innerHTML = `
                <i class="fas ${icon}"></i>
                <span>${message}</span>
            `;

            // Insert at the top of the page
            const container = document.querySelector('.max-w-7xl');
            if (container) {
                container.insertBefore(alertDiv, container.firstChild);

                // Auto remove after 5 seconds
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        }

        // Check for URL parameters and show messages
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('success')) {
                showAlert('تم تنفيذ العملية بنجاح', 'success');
            }
            if (urlParams.get('error')) {
                showAlert('حدث خطأ أثناء تنفيذ العملية', 'error');
            }
        });
    </script>

</body>
</html>
