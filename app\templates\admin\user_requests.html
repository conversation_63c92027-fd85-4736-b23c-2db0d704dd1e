<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلبات {{ target_user.full_name or target_user.username }} - لوحة التحكم</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Modern Arabic font stack */
        body {
            font-family: 'Segoe UI', 'Tahoma', 'Geneva', 'Verdana', sans-serif, 'Arabic UI Text', 'Geeza Pro', 'Traditional Arabic', 'Simplified Arabic';
        }

        /* Enhanced profile avatar */
        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background-size: cover;
            background-position: center;
            background-color: #f3f4f6;
            border: 4px solid #ffffff;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            flex-shrink: 0;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .profile-avatar:hover {
            transform: scale(1.05);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.18);
        }

        /* Enhanced status badges */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 6px 14px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.3px;
            transition: all 0.2s ease;
        }

        .status-badge.pending {
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
            color: #92400e;
            box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);
        }

        .status-badge.in_progress {
            background: linear-gradient(135deg, #60a5fa, #3b82f6);
            color: white;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        .status-badge.completed {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
        }

        .status-badge.rejected {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
        }

        /* Role badges */
        .role-badge {
            display: inline-flex;
            align-items: center;
            padding: 6px 14px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        .role-badge.admin {
            background-color: #fef3c7;
            color: #92400e;
            border: 2px solid #fbbf24;
        }

        .role-badge.manager {
            background-color: #dbeafe;
            color: #1e40af;
            border: 2px solid #3b82f6;
        }

        .role-badge.user {
            background-color: #f3e8ff;
            color: #7c3aed;
            border: 2px solid #8b5cf6;
        }

        /* Enhanced buttons */
        .primary-btn {
            background: linear-gradient(135deg, #06b6d4, #0891b2);
            color: white;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3);
        }

        .primary-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(6, 182, 212, 0.4);
        }

        .secondary-btn {
            background: linear-gradient(135deg, #6b7280, #4b5563);
            color: white;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);
        }

        .secondary-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(107, 114, 128, 0.4);
        }

        /* Enhanced form styling */
        .form-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }

        .form-section {
            padding: 2rem;
            border-bottom: 1px solid #f1f5f9;
        }

        .form-section:last-child {
            border-bottom: none;
        }

        /* Header styling */
        .page-header {
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            color: white;
            border-radius: 20px;
            margin-bottom: 2rem;
            padding: 2.5rem;
            box-shadow: 0 15px 35px rgba(6, 182, 212, 0.3);
        }

        /* Stats cards */
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }

        /* Table styling */
        .requests-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid #e5e7eb;
        }

        .requests-table th {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            padding: 1rem;
            font-weight: 700;
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
        }

        .requests-table td {
            padding: 1rem;
            border-bottom: 1px solid #f1f5f9;
        }

        .requests-table tr:hover {
            background-color: #f8fafc;
        }

        /* Progress bars */
        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        /* Success/Error messages */
        .alert {
            padding: 16px 20px;
            border-radius: 12px;
            margin-bottom: 1.5rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .alert-success {
            background-color: #ecfdf5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .alert-error {
            background-color: #fef2f2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }

        .alert-info {
            background-color: #eff6ff;
            color: #1e40af;
            border: 1px solid #bfdbfe;
        }

        /* Loading animation */
        .loading-shimmer {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        /* Enhanced tooltips */
        [title] {
            position: relative;
            cursor: help;
        }

        [title]:hover::after {
            content: attr(title);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            white-space: pre-line;
            z-index: 1000;
            max-width: 300px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        [title]:hover::before {
            content: '';
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%) translateY(100%);
            border: 5px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.9);
            z-index: 1001;
        }

        /* Enhanced file preview styles */
        .file-item {
            transition: all 0.3s ease;
        }

        .file-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* Modal animations */
        #filesModal, #pdfViewerModal {
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            .form-section {
                padding: 1.5rem;
            }

            .page-header {
                padding: 2rem;
            }

            .stat-card {
                padding: 1rem;
            }

            #filesModal .bg-white, #pdfViewerModal .bg-white {
                margin: 1rem;
                max-height: calc(100vh - 2rem);
            }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Enhanced Header Section -->
        <div class="page-header">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-6 space-x-reverse">
                    <div class="profile-avatar" style="background-image: url('{{ target_user_avatar_url }}');"></div>
                    <div>
                        <h1 class="text-4xl font-bold mb-2">طلبات {{ target_user.full_name or target_user.username }}</h1>
                        <p class="text-blue-100 text-lg mb-3">إدارة ومتابعة جميع طلبات المستخدم</p>
                        <div class="flex items-center space-x-4 space-x-reverse">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <i class="fas fa-envelope text-blue-300"></i>
                                <span class="text-sm">{{ target_user.email }}</span>
                            </div>
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <i class="fas fa-user-tag text-blue-300"></i>
                                <span class="role-badge {{ target_user.role.value }}">
                                    {% if target_user.role.value == 'admin' %}
                                        <i class="fas fa-crown mr-1"></i>
                                        مدير النظام
                                    {% elif target_user.role.value == 'manager' %}
                                        <i class="fas fa-user-tie mr-1"></i>
                                        مدير المشاريع
                                    {% else %}
                                        <i class="fas fa-user mr-1"></i>
                                        عضو الفريق
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex space-x-3 space-x-reverse">
                    <a href="/admin/users/{{ target_user.id }}/edit" class="inline-flex items-center px-4 py-2 bg-white bg-opacity-20 text-white rounded-lg hover:bg-opacity-30 transition-all duration-200">
                        <i class="fas fa-edit ml-2"></i>
                        تعديل المستخدم
                    </a>
                    <a href="/admin/users/table" class="inline-flex items-center px-4 py-2 bg-white bg-opacity-20 text-white rounded-lg hover:bg-opacity-30 transition-all duration-200">
                        <i class="fas fa-arrow-right ml-2"></i>
                        العودة للمستخدمين
                    </a>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Requests -->
            <div class="stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">إجمالي الطلبات</p>
                        <p class="text-3xl font-bold text-blue-600">{{ request_stats.total_requests }}</p>
                        <p class="text-xs text-gray-500 mt-1">جميع الطلبات المسجلة</p>
                    </div>
                    <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-file-alt text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Completed Requests -->
            <div class="stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">الطلبات المكتملة</p>
                        <p class="text-3xl font-bold text-green-600">{{ request_stats.status_distribution.completed or 0 }}</p>
                        <p class="text-xs text-gray-500 mt-1">تم إنجازها بنجاح</p>
                    </div>
                    <div class="w-14 h-14 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-check-circle text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Pending Requests -->
            <div class="stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">قيد المعالجة</p>
                        <p class="text-3xl font-bold text-yellow-600">{{ (request_stats.status_distribution.pending or 0) + (request_stats.status_distribution.in_progress or 0) }}</p>
                        <p class="text-xs text-gray-500 mt-1">تحتاج متابعة</p>
                    </div>
                    <div class="w-14 h-14 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-clock text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Completion Rate -->
            <div class="stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">معدل الإنجاز</p>
                        <p class="text-3xl font-bold text-purple-600">{{ request_stats.overall_completion_rate }}%</p>
                        <p class="text-xs text-gray-500 mt-1">الأداء العام</p>
                    </div>
                    <div class="w-14 h-14 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-chart-pie text-white text-xl"></i>
                    </div>
                </div>
                <!-- Progress Bar -->
                <div class="mt-4">
                    <div class="progress-bar">
                        <div class="progress-fill bg-gradient-to-r from-purple-500 to-purple-600" style="width: {{ request_stats.overall_completion_rate }}%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Time-based Analytics -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <!-- Daily Performance -->
            <div class="form-container">
                <div class="form-section">
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center space-x-4 space-x-reverse">
                            <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-calendar-day text-white text-lg"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-bold text-gray-900">الأداء اليومي</h3>
                                <p class="text-sm text-gray-600">آخر 24 ساعة</p>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">إجمالي الطلبات</span>
                            <span class="text-lg font-bold text-gray-900">{{ request_stats.daily_stats.requests }}</span>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">المكتملة</span>
                            <span class="text-lg font-bold text-green-600">{{ request_stats.daily_stats.completed }}</span>
                        </div>
                        <div class="text-center p-4 bg-gradient-to-r from-orange-50 to-red-50 rounded-lg border border-orange-200">
                            <p class="text-sm text-gray-600 mb-2">معدل الإنجاز اليومي</p>
                            <p class="text-3xl font-bold text-orange-600">{{ request_stats.daily_stats.completion_rate }}%</p>
                            <div class="mt-3">
                                <div class="progress-bar">
                                    <div class="progress-fill bg-gradient-to-r from-orange-500 to-red-600" style="width: {{ request_stats.daily_stats.completion_rate }}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Weekly Performance -->
            <div class="form-container">
                <div class="form-section">
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center space-x-4 space-x-reverse">
                            <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-business-time text-white text-lg"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-bold text-gray-900">الأداء الأسبوعي</h3>
                                <p class="text-sm text-gray-600">آخر 5 أيام عمل</p>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">إجمالي الطلبات</span>
                            <span class="text-lg font-bold text-gray-900">{{ request_stats.weekly_stats.requests }}</span>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">المكتملة</span>
                            <span class="text-lg font-bold text-green-600">{{ request_stats.weekly_stats.completed }}</span>
                        </div>
                        <div class="text-center p-4 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg border border-indigo-200">
                            <p class="text-sm text-gray-600 mb-2">معدل الإنجاز الأسبوعي</p>
                            <p class="text-3xl font-bold text-indigo-600">{{ request_stats.weekly_stats.completion_rate }}%</p>
                            <div class="mt-3">
                                <div class="progress-bar">
                                    <div class="progress-fill bg-gradient-to-r from-indigo-500 to-purple-600" style="width: {{ request_stats.weekly_stats.completion_rate }}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Monthly Performance -->
            <div class="form-container">
                <div class="form-section">
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center space-x-4 space-x-reverse">
                            <div class="w-12 h-12 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-calendar-alt text-white text-lg"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-bold text-gray-900">الأداء الشهري</h3>
                                <p class="text-sm text-gray-600">آخر 30 يوم</p>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">إجمالي الطلبات</span>
                            <span class="text-lg font-bold text-gray-900">{{ request_stats.monthly_stats.requests }}</span>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-700">المكتملة</span>
                            <span class="text-lg font-bold text-green-600">{{ request_stats.monthly_stats.completed }}</span>
                        </div>
                        <div class="text-center p-4 bg-gradient-to-r from-teal-50 to-cyan-50 rounded-lg border border-teal-200">
                            <p class="text-sm text-gray-600 mb-2">معدل الإنجاز الشهري</p>
                            <p class="text-3xl font-bold text-teal-600">{{ request_stats.monthly_stats.completion_rate }}%</p>
                            <div class="mt-3">
                                <div class="progress-bar">
                                    <div class="progress-fill bg-gradient-to-r from-teal-500 to-cyan-600" style="width: {{ request_stats.monthly_stats.completion_rate }}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Search and Filter Section -->
        <div class="form-container mb-8">
            <div class="form-section">
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-search text-white text-lg"></i>
                        </div>
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900">البحث والفلترة</h2>
                            <p class="text-gray-600">ابحث وصفي طلبات المستخدم</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse text-sm text-gray-500">
                        <i class="fas fa-info-circle"></i>
                        <span>{{ user_requests|length }} نتيجة</span>
                    </div>
                </div>

                <form method="GET" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Search Input -->
                        <div class="md:col-span-2">
                            <label for="search" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-search text-gray-400 mr-1"></i>
                                البحث في الطلبات
                            </label>
                            <div class="relative">
                                <input type="text"
                                       id="search"
                                       name="search"
                                       value="{{ current_search or '' }}"
                                       placeholder="ابحث برقم الطلب، الاسم، أو رقم الإجازة..."
                                       class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 pl-10">
                                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>
                        </div>

                        <!-- Status Filter -->
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-filter text-gray-400 mr-1"></i>
                                تصفية بالحالة
                            </label>
                            <div class="relative">
                                <select id="status" name="status" class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 pl-10">
                                    <option value="">جميع الحالات</option>
                                    {% for status in statuses %}
                                    <option value="{{ status }}" {% if current_status == status %}selected{% endif %}>
                                        {% if status == 'pending' %}قيد الانتظار
                                        {% elif status == 'in_progress' %}قيد المعالجة
                                        {% elif status == 'completed' %}مكتمل
                                        {% elif status == 'rejected' %}مرفوض
                                        {% else %}{{ status }}
                                        {% endif %}
                                    </option>
                                    {% endfor %}
                                </select>
                                <i class="fas fa-filter absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-between items-center pt-4 border-t border-gray-200">
                        <div class="flex space-x-3 space-x-reverse">
                            <button type="submit" class="primary-btn">
                                <i class="fas fa-search"></i>
                                بحث
                            </button>
                            <a href="/admin/users/{{ target_user.id }}/requests" class="secondary-btn">
                                <i class="fas fa-times"></i>
                                مسح الفلاتر
                            </a>
                        </div>

                        <!-- Export Options -->
                        <div class="relative">
                            <button type="button" id="exportBtn" class="inline-flex items-center px-4 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors">
                                <i class="fas fa-download mr-2"></i>
                                تصدير البيانات
                            </button>
                            <div id="exportMenu" class="hidden absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                                <a href="/admin/users/{{ target_user.id }}/requests/export?format=csv{% if current_status %}&status={{ current_status }}{% endif %}{% if current_search %}&search={{ current_search }}{% endif %}"
                                   class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-t-lg">
                                    <i class="fas fa-file-csv mr-2"></i>
                                    تصدير CSV
                                </a>
                                <a href="/admin/users/{{ target_user.id }}/requests/export?format=json{% if current_status %}&status={{ current_status }}{% endif %}{% if current_search %}&search={{ current_search }}{% endif %}"
                                   class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-b-lg">
                                    <i class="fas fa-file-code mr-2"></i>
                                    تصدير JSON
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Search Results Info -->
                    {% if current_search or current_status %}
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
                        <div class="flex items-center space-x-3 space-x-reverse">
                            <i class="fas fa-info-circle text-blue-500"></i>
                            <div>
                                <p class="text-sm font-medium text-blue-800">نتائج البحث:</p>
                                <div class="text-sm text-blue-600">
                                    {% if current_search %}
                                    <span>البحث: "{{ current_search }}"</span>
                                    {% endif %}
                                    {% if current_status %}
                                    <span>{% if current_search %} • {% endif %}الحالة:
                                        {% if current_status == 'pending' %}قيد الانتظار
                                        {% elif current_status == 'in_progress' %}قيد المعالجة
                                        {% elif current_status == 'completed' %}مكتمل
                                        {% elif current_status == 'rejected' %}مرفوض
                                        {% endif %}
                                    </span>
                                    {% endif %}
                                    <span> • {{ user_requests|length }} نتيجة</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </form>
            </div>
        </div>

        <!-- Enhanced Requests Table -->
        {% if user_requests %}
        <div class="requests-table mb-8">
            <div class="form-section border-b">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-list text-white text-lg"></i>
                        </div>
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900">قائمة الطلبات</h2>
                            <p class="text-gray-600">جميع طلبات {{ target_user.full_name or target_user.username }}</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse text-sm text-gray-500">
                        <i class="fas fa-info-circle"></i>
                        <span>{{ user_requests|length }} طلب</span>
                    </div>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr>
                            <th class="text-right">رقم الطلب</th>
                            <th class="text-right">الاسم الكامل</th>
                            <th class="text-right">الرقم الشخصي</th>
                            <th class="text-right">المبنى</th>
                            <th class="text-center">الحالة</th>
                            <th class="text-center">المرفقات</th>
                            <th class="text-center">تاريخ الإنشاء</th>
                            <th class="text-center">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for request in user_requests %}
                        <tr class="request-row" data-request-id="{{ request.id }}" data-status="{{ request.status.value }}">
                            <td>
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    <i class="fas fa-hashtag text-gray-400"></i>
                                    <code class="bg-gray-100 px-2 py-1 rounded text-sm font-mono"
                                          title="الرمز التعريفي: {{ request.unique_code }}">{{ request.request_number }}</code>
                                </div>
                            </td>
                            <td>
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    <i class="fas fa-id-card text-gray-400"></i>
                                    <span class="text-gray-700 font-mono">{{ request.personal_number or 'غير محدد' }}</span>
                                </div>
                            </td>
                            <td>
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    <i class="fas fa-user text-gray-400"></i>
                                    <span class="font-medium">{{ request.full_name or request.request_name or 'غير محدد' }}</span>
                                </div>
                            </td>
                            <td>
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    <i class="fas fa-building text-gray-400"></i>
                                    <span class="text-gray-700"
                                          title="المبنى: {{ request.building_name or 'غير محدد' }}&#10;الطريق: {{ request.road_name or 'غير محدد' }}&#10;رقم المجمع: {{ request.building_number or 'غير محدد' }}&#10;رقم إجازة البناء: {{ request.building_permit_number or 'غير محدد' }}">
                                        {{ request.building_name or 'غير محدد' }}
                                    </span>
                                </div>
                            </td>
                            <td class="text-center">
                                <span class="status-badge {{ request.status.value }}">
                                    {% if request.status.value == 'pending' %}
                                        <i class="fas fa-clock mr-1"></i>
                                        قيد الانتظار
                                    {% elif request.status.value == 'in_progress' %}
                                        <i class="fas fa-cog mr-1"></i>
                                        قيد المعالجة
                                    {% elif request.status.value == 'completed' %}
                                        <i class="fas fa-check mr-1"></i>
                                        مكتمل
                                    {% elif request.status.value == 'rejected' %}
                                        <i class="fas fa-times mr-1"></i>
                                        مرفوض
                                    {% endif %}
                                </span>
                            </td>
                            <td class="text-center">
                                <div class="flex items-center justify-center space-x-2 space-x-reverse">
                                    {% if request.files|length > 0 %}
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <div class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-semibold">
                                            {{ request.files|length }}
                                        </div>
                                        <button onclick="showRequestFiles({{ request.id }}, '{{ request.request_number }}')"
                                                class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-lg transition-colors text-sm font-medium"
                                                title="عرض الملفات">
                                            <i class="fas fa-folder-open mr-1"></i>
                                            عرض
                                        </button>
                                    </div>
                                    {% else %}
                                    <span class="text-gray-400 text-sm">لا توجد ملفات</span>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="text-center">
                                <div class="text-sm">
                                    <div class="font-medium text-gray-900">{{ request.created_at.strftime('%Y-%m-%d') }}</div>
                                    <div class="text-gray-500">{{ request.created_at.strftime('%H:%M') }}</div>
                                </div>
                            </td>
                            <td class="text-center">
                                <div class="flex items-center justify-center space-x-2 space-x-reverse">
                                    <!-- View Button -->
                                    <a href="/admin/requests/{{ request.id }}/view"
                                       class="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors text-sm">
                                        <i class="fas fa-eye mr-1"></i>
                                        عرض
                                    </a>

                                    <!-- Edit Button -->
                                    <a href="/admin/requests/{{ request.id }}/edit"
                                       class="inline-flex items-center px-3 py-1 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors text-sm">
                                        <i class="fas fa-edit mr-1"></i>
                                        تعديل
                                    </a>

                                    <!-- Status Update Button -->
                                    {% if request.status.value in ['pending', 'in_progress'] %}
                                    <form method="post" action="/admin/requests/{{ request.id }}/update-status" class="inline">
                                        <input type="hidden" name="status" value="completed">
                                        <button type="submit"
                                                class="inline-flex items-center px-3 py-1 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors text-sm"
                                                onclick="return confirm('هل أنت متأكد من تحديد هذا الطلب كمكتمل؟')">
                                            <i class="fas fa-check mr-1"></i>
                                            إكمال
                                        </button>
                                    </form>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                </tbody>
            </table>
        </div>

        {% else %}
        <!-- Empty State -->
        <div class="form-container">
            <div class="form-section text-center py-12">
                <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-file-alt text-4xl text-gray-400"></i>
                </div>

                <h3 class="text-2xl font-bold text-gray-900 mb-4">
                    {% if current_search or current_status %}
                        لا توجد طلبات تطابق البحث
                    {% else %}
                        لا توجد طلبات لهذا المستخدم
                    {% endif %}
                </h3>

                <p class="text-gray-600 mb-8 max-w-md mx-auto">
                    {% if current_search or current_status %}
                        لا توجد طلبات تطابق معايير البحث المحددة. جرب تعديل الفلاتر أو البحث بكلمات مختلفة.
                    {% else %}
                        لم يقم {{ target_user.full_name or target_user.username }} بإنشاء أي طلبات حتى الآن.
                    {% endif %}
                </p>

                <div class="flex justify-center space-x-4 space-x-reverse">
                    {% if current_search or current_status %}
                    <a href="/admin/users/{{ target_user.id }}/requests" class="secondary-btn">
                        <i class="fas fa-list"></i>
                        عرض جميع الطلبات
                    </a>
                    {% endif %}

                    <a href="/admin/users/{{ target_user.id }}/edit" class="primary-btn">
                        <i class="fas fa-user-edit"></i>
                        تعديل المستخدم
                    </a>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Files Modal -->
    <div id="filesModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
                <!-- Modal Header -->
                <div class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-2xl font-bold">ملفات الطلب</h3>
                            <p id="modalRequestNumber" class="text-blue-100 mt-1"></p>
                        </div>
                        <button onclick="closeFilesModal()" class="text-white hover:text-gray-200 transition-colors">
                            <i class="fas fa-times text-2xl"></i>
                        </button>
                    </div>
                </div>

                <!-- Modal Body -->
                <div class="p-6 max-h-[70vh] overflow-y-auto">
                    <div id="filesContainer" class="space-y-4">
                        <!-- Files will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- PDF Viewer Modal -->
    <div id="pdfViewerModal" class="fixed inset-0 bg-black bg-opacity-75 hidden z-60">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[95vh] overflow-hidden">
                <!-- PDF Viewer Header -->
                <div class="bg-gradient-to-r from-red-600 to-pink-600 text-white p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3 space-x-reverse">
                            <i class="fas fa-file-pdf text-2xl"></i>
                            <div>
                                <h3 id="pdfFileName" class="text-xl font-bold"></h3>
                                <p class="text-red-100 text-sm">استعراض ملف PDF</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <button id="downloadPdfBtn" class="bg-white bg-opacity-20 hover:bg-opacity-30 px-3 py-2 rounded-lg transition-colors">
                                <i class="fas fa-download mr-1"></i>
                                تحميل
                            </button>
                            <button onclick="closePdfViewer()" class="text-white hover:text-gray-200 transition-colors">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- PDF Viewer Body -->
                <div class="bg-gray-100 h-[80vh]">
                    <iframe id="pdfFrame" class="w-full h-full border-0" src=""></iframe>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Export menu toggle
            const exportBtn = document.getElementById('exportBtn');
            const exportMenu = document.getElementById('exportMenu');

            if (exportBtn && exportMenu) {
                exportBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    exportMenu.classList.toggle('hidden');
                });

                // Close export menu when clicking outside
                document.addEventListener('click', function() {
                    exportMenu.classList.add('hidden');
                });

                exportMenu.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            }
        });

        // Copy to clipboard function
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                showAlert(`تم نسخ الرمز: ${text}`, 'success');
            }).catch(function() {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showAlert(`تم نسخ الرمز: ${text}`, 'success');
            });
        }

        // Alert function
        function showAlert(message, type = 'info') {
            // Remove existing alerts
            const existingAlerts = document.querySelectorAll('.alert');
            existingAlerts.forEach(alert => alert.remove());

            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;

            const icon = type === 'success' ? 'fa-check-circle' :
                        type === 'error' ? 'fa-exclamation-triangle' : 'fa-info-circle';

            alertDiv.innerHTML = `
                <i class="fas ${icon}"></i>
                <span>${message}</span>
            `;

            // Insert at the top of the page
            const container = document.querySelector('.max-w-7xl');
            if (container) {
                container.insertBefore(alertDiv, container.firstChild);

                // Auto remove after 5 seconds
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        }

        // Show request files modal
        async function showRequestFiles(requestId, requestNumber) {
            document.getElementById('modalRequestNumber').textContent = `الطلب رقم: ${requestNumber}`;

            try {
                const response = await fetch(`/admin/api/requests/${requestId}/files`);
                const data = await response.json();

                const filesContainer = document.getElementById('filesContainer');

                if (data.files && data.files.length > 0) {
                    filesContainer.innerHTML = data.files.map(file => `
                        <div class="bg-gray-50 rounded-lg p-4 border border-gray-200 hover:border-blue-300 transition-colors">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3 space-x-reverse">
                                    <div class="w-12 h-12 bg-gradient-to-br ${getFileIconColor(file.file_type)} rounded-lg flex items-center justify-center">
                                        <i class="fas ${getFileIcon(file.file_type)} text-white text-lg"></i>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <h4 class="text-lg font-semibold text-gray-900 truncate">${file.original_filename}</h4>
                                        <div class="flex items-center space-x-4 space-x-reverse text-sm text-gray-500 mt-1">
                                            <span><i class="fas fa-tag mr-1"></i>${getCategoryName(file.file_category)}</span>
                                            <span><i class="fas fa-hdd mr-1"></i>${formatFileSize(file.file_size)}</span>
                                            <span><i class="fas fa-calendar mr-1"></i>${formatDate(file.uploaded_at)}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    ${file.file_type === 'pdf' ? `
                                    <button onclick="viewPDF(${file.id}, '${file.original_filename}')"
                                            class="bg-red-100 text-red-700 hover:bg-red-200 px-3 py-2 rounded-lg transition-colors text-sm font-medium">
                                        <i class="fas fa-eye mr-1"></i>
                                        استعراض PDF
                                    </button>
                                    ` : ''}
                                    <a href="/files/view/${file.id}" target="_blank"
                                       class="bg-blue-100 text-blue-700 hover:bg-blue-200 px-3 py-2 rounded-lg transition-colors text-sm font-medium">
                                        <i class="fas fa-external-link-alt mr-1"></i>
                                        فتح
                                    </a>
                                    <a href="/files/download/${file.id}"
                                       class="bg-green-100 text-green-700 hover:bg-green-200 px-3 py-2 rounded-lg transition-colors text-sm font-medium">
                                        <i class="fas fa-download mr-1"></i>
                                        تحميل
                                    </a>
                                </div>
                            </div>
                        </div>
                    `).join('');
                } else {
                    filesContainer.innerHTML = `
                        <div class="text-center py-8">
                            <i class="fas fa-folder-open text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-500">لا توجد ملفات مرفقة بهذا الطلب</p>
                        </div>
                    `;
                }

                document.getElementById('filesModal').classList.remove('hidden');
            } catch (error) {
                console.error('Error loading files:', error);
                showAlert('حدث خطأ في تحميل الملفات', 'error');
            }
        }

        // Close files modal
        function closeFilesModal() {
            document.getElementById('filesModal').classList.add('hidden');
        }

        // View PDF in modal
        function viewPDF(fileId, fileName) {
            document.getElementById('pdfFileName').textContent = fileName;
            document.getElementById('pdfFrame').src = `/files/view/${fileId}`;
            document.getElementById('downloadPdfBtn').onclick = () => window.open(`/files/download/${fileId}`, '_blank');
            document.getElementById('pdfViewerModal').classList.remove('hidden');
        }

        // Close PDF viewer
        function closePdfViewer() {
            document.getElementById('pdfViewerModal').classList.add('hidden');
            document.getElementById('pdfFrame').src = '';
        }

        // Helper functions
        function getFileIcon(fileType) {
            const icons = {
                'pdf': 'fa-file-pdf',
                'doc': 'fa-file-word',
                'docx': 'fa-file-word',
                'txt': 'fa-file-alt',
                'jpg': 'fa-file-image',
                'jpeg': 'fa-file-image',
                'png': 'fa-file-image',
                'gif': 'fa-file-image'
            };
            return icons[fileType.toLowerCase()] || 'fa-file';
        }

        function getFileIconColor(fileType) {
            const colors = {
                'pdf': 'from-red-500 to-red-600',
                'doc': 'from-blue-500 to-blue-600',
                'docx': 'from-blue-500 to-blue-600',
                'txt': 'from-gray-500 to-gray-600',
                'jpg': 'from-green-500 to-green-600',
                'jpeg': 'from-green-500 to-green-600',
                'png': 'from-green-500 to-green-600',
                'gif': 'from-green-500 to-green-600'
            };
            return colors[fileType.toLowerCase()] || 'from-gray-500 to-gray-600';
        }

        function getCategoryName(category) {
            const categories = {
                'architectural_plans': 'مخططات معمارية',
                'electrical_mechanical_plans': 'مخططات كهربائية وميكانيكية',
                'inspection_department': 'قسم التفتيش',
                'fire_equipment_files': 'معدات الحريق',
                'commercial_records_files': 'السجلات التجارية',
                'engineering_offices_files': 'المكاتب الهندسية',
                'hazardous_materials_files': 'المواد الخطرة',
                'general': 'عام'
            };
            return categories[category] || category;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 بايت';
            const k = 1024;
            const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        // Close modals when clicking outside
        document.addEventListener('click', function(e) {
            if (e.target.id === 'filesModal') {
                closeFilesModal();
            }
            if (e.target.id === 'pdfViewerModal') {
                closePdfViewer();
            }
        });

        // Close modals with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeFilesModal();
                closePdfViewer();
            }
        });

        // Check for URL parameters and show messages
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('success')) {
                showAlert('تم تنفيذ العملية بنجاح', 'success');
            }
            if (urlParams.get('error')) {
                showAlert('حدث خطأ أثناء تنفيذ العملية', 'error');
            }
            if (urlParams.get('updated')) {
                showAlert('تم تحديث حالة الطلب بنجاح', 'success');
            }
        });
    </script>

</body>
</html>
