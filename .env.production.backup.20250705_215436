# Production Environment Configuration for CMSVS Internal System
# IMPORTANT: Update all values before deploying to production

# Database Configuration
DATABASE_URL=*************************************/cmsvs_db

# Database Pool Configuration (Optimized for production)
DB_POOL_SIZE=30          # Increased for production load
DB_MAX_OVERFLOW=50       # Higher overflow for peak times
DB_POOL_TIMEOUT=30       # Shorter timeout for production
DB_POOL_RECYCLE=1800     # 30 minutes recycle time

# Security (CRITICAL: Change these values)
SECRET_KEY=CHANGE_THIS_TO_A_STRONG_SECRET_KEY_IN_PRODUCTION_AT_LEAST_32_CHARACTERS_LONG
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=15  # Shorter for production security

# File Upload Configuration
MAX_FILE_SIZE=52428800   # 50MB for production
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,jpg,jpeg,png,gif,xlsx,xls,ppt,pptx
UPLOAD_DIRECTORY=/app/uploads

# Application Configuration
APP_NAME=CMSVS Internal System
APP_VERSION=1.0.0
DEBUG=False              # CRITICAL: Must be False in production
ENVIRONMENT=production

# Admin Configuration (CRITICAL: Change these values)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=Almanna3i

# Network Configuration
ALLOWED_HOSTS=************
CORS_ORIGINS=https://************,https://www.************

# SSL/TLS Configuration
FORCE_HTTPS=False
SECURE_COOKIES=True
HSTS_MAX_AGE=31536000    # 1 year

# Logging Configuration
LOG_LEVEL=WARNING        # Less verbose for production
LOG_FILE=/app/logs/app.log
LOG_MAX_SIZE=52428800    # 50MB
LOG_BACKUP_COUNT=10
LOG_FORMAT=json          # Structured logging for production

# Rate Limiting (Enabled for production)
RATE_LIMIT_ENABLED=True
RATE_LIMIT_REQUESTS=60   # More restrictive for production
RATE_LIMIT_WINDOW=60     # seconds
RATE_LIMIT_BURST=10      # Allow burst requests

# Session Configuration
SESSION_TIMEOUT=900      # 15 minutes for production security
REMEMBER_ME_DURATION=604800  # 7 days instead of 30

# Performance Configuration
ENABLE_GZIP=True
STATIC_FILE_CACHE=3600   # 1 hour cache for static files
API_CACHE_TTL=300        # 5 minutes cache for API responses

# Monitoring and Health Checks
HEALTH_CHECK_ENABLED=True
METRICS_ENABLED=True
PROMETHEUS_PORT=9090

# Email Configuration (if needed)
SMTP_HOST=smtp.yourcompany.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=CHANGE_THIS_EMAIL_PASSWORD
SMTP_USE_TLS=True

# Backup Configuration
BACKUP_ENABLED=True
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_LOCATION=/app/backups

# Worker Configuration
WORKER_PROCESSES=4       # Adjust based on server CPU cores
WORKER_CONNECTIONS=1000
WORKER_TIMEOUT=30

# Database Backup
DB_BACKUP_ENABLED=True
DB_BACKUP_SCHEDULE=0 1 * * *  # Daily at 1 AM
DB_BACKUP_RETENTION_DAYS=30
DB_BACKUP_LOCATION=/app/db_backups

# Security Headers
SECURITY_HEADERS_ENABLED=True
CONTENT_SECURITY_POLICY=default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://unpkg.com; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; connect-src 'self'
X_FRAME_OPTIONS=DENY
X_CONTENT_TYPE_OPTIONS=nosniff
REFERRER_POLICY=strict-origin-when-cross-origin
