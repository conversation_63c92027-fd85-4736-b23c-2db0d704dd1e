{% for req in requests %} <tr><td><code>{{ req.request_number }}</code></td><td><div><code>{{ req.unique_code }}</code><button data-unique-code="{{ req.unique_code }}"></button></div></td><td>{{ req.request_title }}</td><td> {% if req.status.value == 'pending' %} <span>قيد المراجعة</span> {% elif req.status.value == 'in_' %} <span>قيد التنفيذ</span> {% elif req.status.value == 'completed' %} <span>مكتمل</span> {% elif req.status.value == 'rejected' %} <span>مرفوض</span> {% endif %} </td><td>{{ req.created_at.strftime('%Y-%%d') }}</td><td><div><a href="/requests/{{ req.id }}"> عرض </a> {% if req.status.value == 'pending' %} <a href="/requests/{{ req.id }}/edit"> تعديل </a> {% endif %} </div></td></tr> {% endfor %} {% if requests %} <!-- Load More Button Row --><tr id="loa"><td="6"><button hx-get="/api/requests/loa?skip={{ next_skip }}" hx-target="#requests-tbody" hx-swap="beforeend" hx-indicator="#" onclick="this.closest('tr').remove()"> تحميل المزيد </button><div id><div role="status"><span>جاري التحميل...</span></div></div></td></tr> {% endif %} 