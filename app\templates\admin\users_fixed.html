{% extends "base.html" %} {% block title %}إدارة المستخدمين - CMSVS{% endblock %} {% block content %} <div><!-- Soft UI Header --><div><div><div><div><h1> إدارة المستخدمين </h1><p>إدارة وتتبع جميع المستخدمين في النظام بسهولة وأمان</p></div><div><a href="///new"> إضافة مستخدم جديد </a></div></div></div></div><div><!-- Soft UI Statistics Cards --><div><div><div><div><div><div><h6>إجمالي المستخدمين</h6><h2>{{|length }}</h2><p><span> +12% </span> منذ الشهر الماضي </p></div><div></div></div></div></div></div><div><div><div><div><div><h6>المستخدمون النشطون</h6><h2>{{|selectattr("is_active")|list|length }}</h2><p><span> نشط </span> من إجمالي المستخدمين </p></div><div></div></div></div></div></div><div><div><div><div><div><h6>المستخدمون غير النشطون</h6><h2>{{|rejectattr("is_active")|list|length }}</h2><p><span> معطل </span> يحتاج مراجعة </p></div><div></div></div></div></div></div><div><div><div><div><div><h6>المديرون</h6><h2>{{|selectattr("role.value", "equalto", "")|list|length }}</h2><p><span> مدير </span> صلاحيات كاملة </p></div><div></div></div></div></div></div></div> {% if %} <!-- Soft UI Filters Section --><div><div><h5> البحث والتصفية </h5><p>ابحث وصفي المستخدمين حسب الاسم، البريد الإلكتروني، الدور أو الحالة</p></div><div><div><div><div><label>البحث السريع</label><div><span></span><i></div></div></div><div><div><label>تصفية حسب الدور</label><select id="roleFilter"><option value>جميع الأدوار</option><option value>مدير النظام</option><option value>مستخدم عادي</option></select></div></div><div><div><label>تصفية حسب الحالة</label><select id="statusFilter"><option value>جميع الحالات</option><option value="active">نشط</option><option value="inactive">غير نشط</option></select></div></div></div></div></div><!-- Soft UI Bulk Actions --><div><form method="post" action="///bulk-action" id="bulkUserForm"><div><h6> العمليات المجمعة </h6><div><span><span id="selectedCount">0</span> محدد </span></div></div><div><div><div><label>اختر العملية المطلوبة</label><select name="action"><option value>اختر العملية</option><option value="activate">تفعيل المستخدمين</option><option value="deactivate">إلغاء تفعيل المستخدمين</option><option value="make_">ترقية إلى مدير نظام</option><option value="make_">تحويل إلى مستخدم عادي</option></select></div><div><button type="submit" id="bulkActionBtn" disabled> تطبيق العملية </button></div></div></div></form></div><!-- Soft UI Users Table --><div><div><h5> قائمة المستخدمين </h5><div><button onclick="location.reload()"> تحديث </button></div></div><div>< id="Table"><thead><tr><th><div><i><label for="selectAll"></label></div></th><th>المستخدم</th><th>البريد الإلكتروني</th><th>الاسم الكامل</th><th>الدور</th><th>الحالة</th><th>تاريخ الإنشاء</th><th>الإجراءات</th></tr></thead><tbody> {% for in %} {% set_ ~ ((loop.index % 6) + 1) %} <tr data-name="{{. }}" data-email="{{.email }}" data-' %}مدير النظام{% else %}مستخدم{% endif %}"><td> {% if.id != current_.id %} <div><i><label for="{{.id }}"></label></div> {% else %} {% endif %} </td><td><div><div> {{.[:2].upper() }} </div><div><h6>{{. }}</h6><span>ID: {{.id }}</span> {% if.id == current_.id %} <span>أنت</span> {% endif %} </div></div></td> 