{% extends "base.html" %}

{% block title %}الإعدادات - CMSVS{% endblock %}

{% block content %}
<style>
/* Professional Settings Page Styling */
.page-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
    background: #f8fafc;
    min-height: 100vh;
}

.page-header {
    text-align: right;
    padding: 32px 24px;
    margin-bottom: 32px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.page-title {
    font-size: 32px;
    font-weight: 700;
    margin: 0 0 8px 0;
    color: #1f2937;
}

.page-subtitle {
    font-size: 16px;
    color: #6b7280;
    margin: 0;
}

.settings-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-bottom: 24px;
}

.settings-section {
    background: #ffffff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-title {
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 16px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-group {
    margin-bottom: 16px;
}

.form-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
}

.form-input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn {
    padding: 10px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border: none;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    text-decoration: none;
}

.btn-primary {
    background: #3b82f6;
    color: #ffffff;
}

.btn-primary:hover {
    background: #2563eb;
}

.btn-secondary {
    background: #6b7280;
    color: #ffffff;
}

.btn-secondary:hover {
    background: #4b5563;
}

.alert {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 16px;
    font-size: 14px;
}

.alert-success {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.alert-error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
    margin-top: 16px;
}

.stat-card {
    background: #f9fafb;
    padding: 16px;
    border-radius: 8px;
    text-align: center;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #1f2937;
}

.stat-label {
    font-size: 12px;
    color: #6b7280;
    margin-top: 4px;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.checkbox {
    width: 16px;
    height: 16px;
}

@media (max-width: 768px) {
    .page-container {
        padding: 16px;
    }
    
    .settings-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<div class="page-container">
    <!-- Page Header -->
    <header class="page-header">
        <div style="display: flex; justify-content: space-between; align-items: flex-start; gap: 20px;">
            <div style="flex: 1;">
                <h1 class="page-title">الإعدادات</h1>
                <p class="page-subtitle">إدارة حسابك وتفضيلاتك الشخصية</p>
            </div>
            <div style="display: flex; gap: 12px; flex-wrap: wrap;">
                <a href="/dashboard" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i>
                    العودة للوحة التحكم
                </a>
            </div>
        </div>
    </header>

    <!-- Success/Error Messages -->
    {% if request.query_params.get('success') %}
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i>
        {% if request.query_params.get('success') == 'profile_updated' %}
        تم تحديث الملف الشخصي بنجاح
        {% elif request.query_params.get('success') == 'password_changed' %}
        تم تغيير كلمة المرور بنجاح
        {% elif request.query_params.get('success') == 'preferences_updated' %}
        تم تحديث التفضيلات بنجاح
        {% endif %}
    </div>
    {% endif %}

    {% if request.query_params.get('error') %}
    <div class="alert alert-error">
        <i class="fas fa-exclamation-circle"></i>
        {% if request.query_params.get('error') == 'password_mismatch' %}
        كلمات المرور غير متطابقة
        {% elif request.query_params.get('error') == 'password_too_short' %}
        كلمة المرور قصيرة جداً (6 أحرف على الأقل)
        {% elif request.query_params.get('error') == 'invalid_current_password' %}
        كلمة المرور الحالية غير صحيحة
        {% elif request.query_params.get('error') == 'update_failed' %}
        فشل في تحديث البيانات
        {% else %}
        حدث خطأ في الخادم
        {% endif %}
    </div>
    {% endif %}

    <!-- Settings Grid -->
    <div class="settings-grid">
        <!-- Profile Settings -->
        <div class="settings-section">
            <h2 class="section-title">
                <i class="fas fa-user" style="color: #3b82f6;"></i>
                معلومات الملف الشخصي
            </h2>
            <p style="color: #6b7280; margin-bottom: 16px; font-size: 14px;">
                يمكنك تحديث معلومات حسابك الأساسية هنا
            </p>
            <form method="post" action="/settings/profile">
                <div class="form-group">
                    <label class="form-label">اسم المستخدم</label>
                    <input type="text" name="username" class="form-input" value="{{ user_data.username or '' }}" readonly style="background: #f9fafb; color: #6b7280;">
                    <small style="color: #6b7280; font-size: 12px;">لا يمكن تغيير اسم المستخدم</small>
                </div>
                <div class="form-group">
                    <label class="form-label">الاسم الكامل</label>
                    <input type="text" name="full_name" class="form-input" value="{{ user_data.full_name or '' }}" required>
                </div>
                <div class="form-group">
                    <label class="form-label">البريد الإلكتروني</label>
                    <input type="email" name="email" class="form-input" value="{{ user_data.email or '' }}" required>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    حفظ التغييرات
                </button>
            </form>
        </div>

        <!-- Password Settings -->
        <div class="settings-section">
            <h2 class="section-title">
                <i class="fas fa-lock" style="color: #ef4444;"></i>
                تغيير كلمة المرور
            </h2>
            <form method="post" action="/settings/password">
                <div class="form-group">
                    <label class="form-label">كلمة المرور الحالية</label>
                    <input type="password" name="current_password" class="form-input" required>
                </div>
                <div class="form-group">
                    <label class="form-label">كلمة المرور الجديدة</label>
                    <input type="password" name="new_password" class="form-input" required>
                </div>
                <div class="form-group">
                    <label class="form-label">تأكيد كلمة المرور الجديدة</label>
                    <input type="password" name="confirm_password" class="form-input" required>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-key"></i>
                    تغيير كلمة المرور
                </button>
            </form>
        </div>
    </div>

    <!-- Preferences Settings -->
    <div class="settings-grid">
        <div class="settings-section">
            <h2 class="section-title">
                <i class="fas fa-cog" style="color: #8b5cf6;"></i>
                التفضيلات
            </h2>
            <form method="post" action="/settings/preferences">
                <div class="checkbox-group">
                    <input type="checkbox" name="notifications_enabled" class="checkbox" id="notifications" checked>
                    <label for="notifications" class="form-label" style="margin: 0;">تفعيل الإشعارات</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" name="email_notifications" class="checkbox" id="email_notifications" checked>
                    <label for="email_notifications" class="form-label" style="margin: 0;">إشعارات البريد الإلكتروني</label>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    حفظ التفضيلات
                </button>
            </form>
        </div>

        <div class="settings-section">
            <h2 class="section-title">
                <i class="fas fa-download" style="color: #f59e0b;"></i>
                تصدير البيانات
            </h2>
            <p style="color: #6b7280; margin-bottom: 16px; font-size: 14px;">
                يمكنك تصدير بياناتك الشخصية وطلباتك
            </p>
            <div style="display: flex; gap: 12px; flex-wrap: wrap;">
                <a href="/export/requests" class="btn btn-secondary">
                    <i class="fas fa-file-csv"></i>
                    تصدير الطلبات
                </a>
                <a href="/export/profile" class="btn btn-secondary">
                    <i class="fas fa-user"></i>
                    تصدير الملف الشخصي
                </a>
            </div>
        </div>
    </div>

    <!-- Account Information -->
    <div class="settings-section">
        <h2 class="section-title">
            <i class="fas fa-info-circle" style="color: #10b981;"></i>
            معلومات الحساب والإحصائيات
        </h2>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px;">
            <div>
                <h3 style="margin: 0 0 12px 0; font-size: 16px;">معلومات الحساب</h3>
                <p><strong>نوع الحساب:</strong> {{ user_data.role }}</p>
                <p><strong>تاريخ الإنشاء:</strong> {{ user_data.created_at.strftime('%Y-%m-%d') if user_data.created_at else 'غير محدد' }}</p>
                <p><strong>حالة الحساب:</strong>
                    {% if user_data.is_active %}
                    <span style="color: #10b981;">نشط</span>
                    {% else %}
                    <span style="color: #ef4444;">غير نشط</span>
                    {% endif %}
                </p>
            </div>
            <div>
                <h3 style="margin: 0 0 12px 0; font-size: 16px;">إحصائيات الحساب</h3>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value">{{ user_stats.total_requests if user_stats else 0 }}</div>
                        <div class="stat-label">إجمالي الطلبات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">{{ user_stats.pending_requests if user_stats else 0 }}</div>
                        <div class="stat-label">طلبات معلقة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">{{ user_stats.completed_requests if user_stats else 0 }}</div>
                        <div class="stat-label">طلبات مكتملة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">{{ user_stats.rejected_requests if user_stats else 0 }}</div>
                        <div class="stat-label">طلبات مرفوضة</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Information -->
    <div class="settings-section">
        <h2 class="section-title">
            <i class="fas fa-server" style="color: #6b7280;"></i>
            معلومات النظام
        </h2>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px;">
            <div>
                <p><strong>اسم التطبيق:</strong> {{ app_settings.app_name }}</p>
                <p><strong>إصدار التطبيق:</strong> {{ app_settings.app_version }}</p>
                <p><strong>الحد الأقصى لحجم الملف:</strong> {{ (app_settings.max_file_size / 1024 / 1024)|round(1) }} ميجابايت</p>
            </div>
            <div>
                <p><strong>أنواع الملفات المسموحة:</strong></p>
                <div style="display: flex; flex-wrap: wrap; gap: 4px; margin-top: 8px;">
                    {% for file_type in app_settings.allowed_file_types %}
                    <span style="background: #f3f4f6; padding: 4px 8px; border-radius: 4px; font-size: 12px; color: #374151;">
                        .{{ file_type }}
                    </span>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
