{% extends "base.html" %}

{% block title %}{{ page_title or 'إنشاء رسالة جديدة' }} - CMSVS{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">إنشاء رسالة جديدة</h1>
            <p class="text-gray-600 mt-2">إرسال رسالة إلى مستخدم آخر في النظام</p>
        </div>
        <div>
            <a href="/messages"
               class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-arrow-right ml-2"></i>
                العودة للرسائل
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2">
            <!-- Compose Form -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">
                    <i class="fas fa-envelope text-blue-600 ml-2"></i>
                    تفاصيل الرسالة
                </h2>

                <form id="composeMessageForm" method="post" action="/messages/compose" class="space-y-6">
                    <!-- Recipient Selection -->
                    <div>
                        <label for="recipientSelect" class="block text-sm font-medium text-gray-700 mb-2">
                            المستلم <span class="text-red-500">*</span>
                        </label>
                        <select id="recipientSelect"
                                name="recipient_id"
                                required
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                            <option value="">اختر المستلم...</option>
                            {% for recipient in recipients %}
                            <option value="{{ recipient.id }}"
                                    {% if selected_recipient and selected_recipient.id == recipient.id %}selected{% endif %}>
                                {{ recipient.full_name }} (@{{ recipient.username }}) - {{ recipient.role.value }}
                            </option>
                            {% endfor %}
                        </select>
                        <p class="text-xs text-gray-500 mt-1">اختر المستخدم الذي تريد إرسال الرسالة إليه</p>
                    </div>

                    <!-- Subject -->
                    <div>
                        <label for="messageSubject" class="block text-sm font-medium text-gray-700 mb-2">
                            موضوع الرسالة <span class="text-red-500">*</span>
                        </label>
                        <input type="text"
                               id="messageSubject"
                               name="subject"
                               required
                               placeholder="موضوع الرسالة..."
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                        <p class="text-xs text-gray-500 mt-1">موضوع مختصر يوضح محتوى الرسالة</p>
                    </div>

                    <!-- Message Content -->
                    <div>
                        <label for="messageContent" class="block text-sm font-medium text-gray-700 mb-2">
                            نص الرسالة <span class="text-red-500">*</span>
                        </label>
                        <textarea id="messageContent"
                                  name="content"
                                  rows="8"
                                  placeholder="اكتب رسالتك هنا..."
                                  required
                                  class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-vertical"></textarea>
                        <p class="text-xs text-gray-500 mt-1">محتوى الرسالة التفصيلي</p>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-between items-center pt-6 border-t border-gray-200">
                        <a href="/messages"
                           class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg transition-colors">
                            <i class="fas fa-times ml-2"></i>
                            إلغاء
                        </a>
                        <div class="flex space-x-3 space-x-reverse">
                            <button type="button"
                                    id="saveDraftBtn"
                                    class="bg-yellow-600 hover:bg-yellow-700 text-white px-6 py-2 rounded-lg transition-colors">
                                <i class="fas fa-save ml-2"></i>
                                حفظ كمسودة
                            </button>
                            <button type="submit"
                                    id="sendMessageBtn"
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                                <i class="fas fa-paper-plane ml-2"></i>
                                إرسال الرسالة
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Recipients -->
            {% if recipients %}
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-users text-green-600 ml-2"></i>
                    مستخدمون متاحون
                </h3>
                <div class="space-y-3">
                    {% for recipient in recipients[:6] %}
                    <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                         data-recipient-id="{{ recipient.id }}">
                        <div class="flex items-center space-x-3 space-x-reverse">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <span class="text-blue-600 font-semibold text-xs">
                                    {{ recipient.full_name[0] if recipient.full_name else 'غ' }}{{ recipient.full_name.split(' ')[1][0] if recipient.full_name and recipient.full_name.split(' ')|length > 1 else '' }}
                                </span>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-900">{{ recipient.full_name }}</div>
                                <div class="text-xs text-gray-500">{{ recipient.role.value }}</div>
                            </div>
                        </div>
                        <button onclick="selectRecipient({{ recipient.id }}, '{{ recipient.full_name }}')"
                                class="text-blue-600 hover:text-blue-800 transition-colors text-sm">
                            <i class="fas fa-plus"></i>
                            اختيار
                        </button>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Message Guidelines -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-info-circle text-blue-600 ml-2"></i>
                    إرشادات الرسائل
                </h3>
                <div class="space-y-3">
                    <div class="flex items-start space-x-2 space-x-reverse">
                        <i class="fas fa-check-circle text-green-500 mt-1"></i>
                        <div class="text-sm text-gray-700">
                            استخدم موضوعاً واضحاً ومختصراً
                        </div>
                    </div>
                    <div class="flex items-start space-x-2 space-x-reverse">
                        <i class="fas fa-check-circle text-green-500 mt-1"></i>
                        <div class="text-sm text-gray-700">
                            اكتب محتوى مفهوم ومهذب
                        </div>
                    </div>
                    <div class="flex items-start space-x-2 space-x-reverse">
                        <i class="fas fa-check-circle text-green-500 mt-1"></i>
                        <div class="text-sm text-gray-700">
                            تأكد من اختيار المستلم الصحيح
                        </div>
                    </div>
                    <div class="flex items-start space-x-2 space-x-reverse">
                        <i class="fas fa-info-circle text-blue-500 mt-1"></i>
                        <div class="text-sm text-gray-700">
                            يمكن حفظ الرسالة كمسودة للعودة إليها لاحقاً
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-chart-bar text-purple-600 ml-2"></i>
                    إحصائيات سريعة
                </h3>
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">المستخدمون المتاحون:</span>
                        <span class="font-semibold text-gray-900">{{ recipients|length }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">رسائلك المرسلة:</span>
                        <span class="font-semibold text-blue-600">{{ sent_count or 0 }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">رسائلك الواردة:</span>
                        <span class="font-semibold text-green-600">{{ received_count or 0 }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div id="successModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl shadow-xl max-w-md w-full">
            <div class="p-6 text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                    <i class="fas fa-check text-green-600 text-xl"></i>
                </div>
                <h3 id="successModalLabel" class="text-lg font-medium text-gray-900 mb-2">تم الإرسال بنجاح</h3>
                <p class="text-gray-500 mb-6">سيتم إشعار المستلم بالرسالة الجديدة</p>
                <div class="flex space-x-3 space-x-reverse">
                    <a href="/messages"
                       class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors text-center">
                        الذهاب للرسائل
                    </a>
                    <button type="button"
                            onclick="window.location.reload()"
                            class="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                        رسالة جديدة
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Select recipient from quick list
function selectRecipient(recipientId, recipientName) {
    const select = document.getElementById('recipientSelect');
    select.value = recipientId;

    // Visual feedback
    const button = event.target.closest('button');
    button.innerHTML = '<i class="fas fa-check"></i> تم الاختيار';
    button.classList.remove('text-blue-600', 'hover:text-blue-800');
    button.classList.add('text-green-600');

    setTimeout(() => {
        button.innerHTML = '<i class="fas fa-plus"></i> اختيار';
        button.classList.remove('text-green-600');
        button.classList.add('text-blue-600', 'hover:text-blue-800');
    }, 2000);
}

// Form submission
document.getElementById('composeMessageForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const submitBtn = document.getElementById('sendMessageBtn');

    // Show loading state
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i> جاري الإرسال...';
    submitBtn.disabled = true;

    fetch('/messages/compose', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('successModal').classList.remove('hidden');
        } else {
            alert('حدث خطأ أثناء إرسال الرسالة: ' + (data.message || 'خطأ غير معروف'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء إرسال الرسالة');
    })
    .finally(() => {
        // Reset button state
        submitBtn.innerHTML = '<i class="fas fa-paper-plane ml-2"></i> إرسال الرسالة';
        submitBtn.disabled = false;
    });
});

// Save draft functionality
document.getElementById('saveDraftBtn').addEventListener('click', function() {
    const formData = new FormData(document.getElementById('composeMessageForm'));
    formData.append('save_draft', 'true');

    const btn = this;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i> جاري الحفظ...';
    btn.disabled = true;

    fetch('/messages/save-draft', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            btn.innerHTML = '<i class="fas fa-check ml-2"></i> تم الحفظ';
            btn.classList.remove('bg-yellow-600', 'hover:bg-yellow-700');
            btn.classList.add('bg-green-600', 'hover:bg-green-700');

            setTimeout(() => {
                btn.innerHTML = '<i class="fas fa-save ml-2"></i> حفظ كمسودة';
                btn.classList.remove('bg-green-600', 'hover:bg-green-700');
                btn.classList.add('bg-yellow-600', 'hover:bg-yellow-700');
            }, 3000);
        } else {
            alert('حدث خطأ أثناء حفظ المسودة');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء حفظ المسودة');
    })
    .finally(() => {
        btn.disabled = false;
    });
});

// Character counter for message content
document.getElementById('messageContent').addEventListener('input', function() {
    const maxLength = 1000;
    const currentLength = this.value.length;

    // You can add a character counter here if needed
    if (currentLength > maxLength) {
        this.value = this.value.substring(0, maxLength);
    }
});
</script>
{% endblock %}