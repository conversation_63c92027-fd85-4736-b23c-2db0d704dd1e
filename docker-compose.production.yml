version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    container_name: cmsvs_db_prod
    restart: unless-stopped
    environment:
      POSTGRES_DB: cmsvs_db
      POSTGRES_USER: cmsvs_user
      POSTGRES_PASSWORD: TfEcqHm7OeQUxHoQDMRYGXsFm
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256 --auth-local=scram-sha-256"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sh:/docker-entrypoint-initdb.d/init-db.sh:ro
    ports:
      - "127.0.0.1:5433:5432"  # Use port 5433 to avoid conflicts
    networks:
      - cmsvs_network

    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U cmsvs_user -d cmsvs_db"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    security_opt:
      - no-new-privileges:true
    tmpfs:
      - /tmp
      - /var/run/postgresql

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: cmsvs_redis_prod
    restart: unless-stopped
    env_file:
      - .env.production
    command: >
      redis-server
      --requirepass 5IdBdB2AWPKuIigfjEujtB6Gd
      --appendonly yes
      --appendfsync everysec
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - cmsvs_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    security_opt:
      - no-new-privileges:true

  # CMSVS Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: cmsvs_app_prod
    restart: unless-stopped
    environment:
      - DATABASE_URL=*********************************************************/cmsvs_db
      - REDIS_URL=redis://:5IdBdB2AWPKuIigfjEujtB6Gd@redis:6379/0
    env_file:
      - .env.production
    volumes:
      - app_uploads:/app/uploads
      - app_logs:/app/logs
      - app_backups:/app/backups
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - cmsvs_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    security_opt:
      - no-new-privileges:true
    user: "1000:1000"

  # Nginx Reverse Proxy
  nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile
    container_name: cmsvs_nginx_prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      app:
        condition: service_healthy
    networks:
      - cmsvs_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    security_opt:
      - no-new-privileges:true

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  app_uploads:
    driver: local
  app_logs:
    driver: local
  app_backups:
    driver: local
  nginx_logs:
    driver: local

networks:
  cmsvs_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
