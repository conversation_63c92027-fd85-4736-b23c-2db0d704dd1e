# PostgreSQL Client Authentication Configuration File
# Security-hardened configuration for production

# TYPE  DATABASE        USER            ADDRESS                 METHOD

# Local connections (Unix domain sockets)
local   all             postgres                                peer
local   all             all                                     scram-sha-256

# IPv4 local connections
host    all             postgres        127.0.0.1/32            scram-sha-256
host    all             postgres        ::1/128                 scram-sha-256

# Docker network connections (restrict to application user only)
host    cmsvs_db        cmsvs_user      **********/16           scram-sha-256

# Deny all other connections
host    all             all             0.0.0.0/0               reject
host    all             all             ::/0                    reject

# Replication connections (if needed for backup)
# host    replication     postgres        127.0.0.1/32            scram-sha-256
# host    replication     postgres        ::1/128                 scram-sha-256
