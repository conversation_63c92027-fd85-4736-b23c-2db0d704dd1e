{% extends "base.html" %}

{% block title %}طلب جديد - CMSVS{% endblock %}

{% block content %}
<style>
/* Professional New Request Styling */
.page-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background-color: #ffffff;
    min-height: 100vh;
}

.page-header {
    background: #ffffff;
    border-bottom: 2px solid #e5e7eb;
    padding: 24px 0;
    margin-bottom: 32px;
}

.page-title {
    font-size: 28px;
    font-weight: 700;
    color: #111827;
    margin: 0 0 8px 0;
}

.page-subtitle {
    color: #6b7280;
    font-size: 16px;
    margin: 0;
}

.breadcrumb {
    background: #f9fafb;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 24px;
    border: 1px solid #e5e7eb;
}

.breadcrumb a {
    color: #3b82f6;
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.section {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 24px;
}

.section-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-content {
    padding: 24px;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-help {
    font-size: 12px;
    color: #6b7280;
    margin-top: 4px;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
}

.checkbox-input {
    width: 16px;
    height: 16px;
}

.checkbox-label {
    font-weight: 500;
    color: #374151;
    font-size: 14px;
}

.upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    padding: 24px;
    text-align: center;
    background: #f9fafb;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #3b82f6;
    background: #eff6ff;
}

.upload-icon {
    width: 48px;
    height: 48px;
    background-color: #3b82f6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    color: #ffffff;
    font-size: 20px;
}

.file-preview {
    margin-top: 16px;
    max-height: 200px;
    overflow-y: auto;
}

.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    margin-bottom: 8px;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.file-icon {
    width: 32px;
    height: 32px;
    background-color: #dc2626;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 14px;
}

.file-details {
    flex: 1;
}

.file-name {
    font-weight: 500;
    color: #111827;
    font-size: 14px;
    margin-bottom: 2px;
}

.file-size {
    font-size: 12px;
    color: #6b7280;
}

.btn {
    padding: 10px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s;
}

.btn-primary {
    background-color: #3b82f6;
    color: #ffffff;
}

.btn-primary:hover {
    background-color: #2563eb;
}

.btn-secondary {
    background-color: #6b7280;
    color: #ffffff;
}

.btn-secondary:hover {
    background-color: #4b5563;
}

.btn-outline {
    background-color: transparent;
    border: 1px solid #d1d5db;
    color: #374151;
}

.btn-outline:hover {
    background-color: #f9fafb;
}

.subsection {
    border-left: 4px solid #e5e7eb;
    padding-left: 16px;
    margin-top: 16px;
}

.subsection.active {
    border-left-color: #3b82f6;
}

@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }

    .page-container {
        padding: 16px;
    }
}
</style>

<!-- Professional Page Container -->
<div class="page-container">
    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb">
        <a href="/dashboard">لوحة التحكم</a> /
        <span>طلب جديد</span>
    </nav>

    <!-- Page Header -->
    <header class="page-header">
        <div style="display: flex; justify-content: space-between; align-items: flex-start; gap: 20px;">
            <div style="flex: 1;">
                <h1 class="page-title">إنشاء طلب جديد - الدفاع المدني</h1>
                <p class="page-subtitle">املأ النموذج وارفق المستندات المطلوبة</p>
            </div>
            <div style="text-align: right;">
                <div style="font-size: 12px; color: #6b7280; margin-bottom: 4px;">رقم الطلب المؤقت:</div>
                <div id="tempRequestNumber" style="font-weight: 500; color: #374151;">سيتم إنشاؤه عند الإرسال</div>
            </div>
        </div>
    </header>

    <form method="post" action="/requests/new" enctype="multipart/form-data" id="newRequestForm">
        <!-- Personal Information Section -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-user" style="color: #10b981;"></i>
                    المعلومات الشخصية
                </h2>
            </div>
            <div class="section-content">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="full_name" class="form-label">
                            الإسم الثلاثي <span style="color: #ef4444;">*</span>
                        </label>
                        <input type="text" id="full_name" name="full_name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="personal_number" class="form-label">
                            الرقم الشخصي <span style="color: #ef4444;">*</span>
                        </label>
                        <input type="text" id="personal_number" name="personal_number" class="form-control" required pattern="[0-9]{9}" maxlength="9">
                        <div class="form-help">يجب أن يكون 9 أرقام بالضبط</div>
                    </div>
                    <div class="form-group">
                        <label for="phone_number" class="form-label">رقم الهاتف</label>
                        <input type="tel" id="phone_number" name="phone_number" class="form-control">
                    </div>
                </div>
            </div>
        </div>

        <!-- Building Information Section -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-building" style="color: #f59e0b;"></i>
                    معلومات المبنى
                </h2>
            </div>
            <div class="section-content">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="building_name" class="form-label">المبنى</label>
                        <input type="text" id="building_name" name="building_name" class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="road_name" class="form-label">الطريق</label>
                        <input type="text" id="road_name" name="road_name" class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="building_number" class="form-label">المجمع</label>
                        <input type="text" id="building_number" name="building_number" class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="civil_defense_file_number" class="form-label">رقم ملف الدفاع المدني</label>
                        <input type="text" id="civil_defense_file_number" name="civil_defense_file_number" class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="building_permit_number" class="form-label">رقم إجازة البناء</label>
                        <input type="text" id="building_permit_number" name="building_permit_number" class="form-control">
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Information Section -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-info-circle" style="color: #6366f1;"></i>
                    معلومات إضافية
                </h2>
            </div>
            <div class="section-content">
                <div class="form-group">
                    <label for="description" class="form-label">
                        وصف الطلب
                    </label>
                    <textarea id="description"
                              name="description"
                              rows="4"
                              placeholder="اكتب وصفاً تفصيلياً للطلب..."
                              class="form-control"></textarea>
                    <div class="form-help">وصف تفصيلي لمحتوى الطلب ومتطلباته</div>
                </div>
            </div>
        </div>

        <!-- Required File Upload Sections -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-paperclip" style="color: #3b82f6;"></i>
                    المرفقات المطلوبة
                </h2>
            </div>
            <div class="section-content">
                <!-- Architectural Engineering Plans -->
                <div class="form-group">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                        <label class="form-label">
                            مخططات هندسية معمارية <span style="color: #ef4444;">*</span>
                        </label>
                        <span id="architectural_plans_count" style="font-size: 12px; color: #6b7280;">0 ملف</span>
                    </div>
                    <input type="file" id="architectural_plans" name="architectural_plans" multiple accept=".pdf" style="display: none;">
                    <div class="upload-area" data-input-id="architectural_plans" data-preview-id="architectural_plans_preview">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <h3 style="margin: 0 0 8px 0; font-size: 16px; font-weight: 600;">اختر ملفات PDF أو اسحبها هنا</h3>
                        <p style="margin: 0; color: #6b7280; font-size: 14px;">حد أقصى 10 ميجابايت لكل ملف</p>
                    </div>
                    <div id="architectural_plans_preview" class="file-preview"></div>
                </div>

                <!-- Electrical & Mechanical Engineering Plans -->
                <div class="form-group">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                        <label class="form-label">
                            مخططات هندسية كهربائية وميكانيكية <span style="color: #ef4444;">*</span>
                        </label>
                        <span id="electrical_mechanical_plans_count" style="font-size: 12px; color: #6b7280;">0 ملف</span>
                    </div>
                    <input type="file" id="electrical_mechanical_plans" name="electrical_mechanical_plans" multiple accept=".pdf" style="display: none;">
                    <div class="upload-area" data-input-id="electrical_mechanical_plans" data-preview-id="electrical_mechanical_plans_preview">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <h3 style="margin: 0 0 8px 0; font-size: 16px; font-weight: 600;">اختر ملفات PDF أو اسحبها هنا</h3>
                        <p style="margin: 0; color: #6b7280; font-size: 14px;">حد أقصى 10 ميجابايت لكل ملف</p>
                    </div>
                    <div id="electrical_mechanical_plans_preview" class="file-preview"></div>
                </div>

                <!-- Inspection Department -->
                <div class="form-group">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                        <label class="form-label">
                            قسم التفتيش <span style="color: #ef4444;">*</span>
                        </label>
                        <span id="inspection_department_count" style="font-size: 12px; color: #6b7280;">0 ملف</span>
                    </div>
                    <input type="file" id="inspection_department" name="inspection_department" multiple accept=".pdf" style="display: none;">
                    <div class="upload-area" data-input-id="inspection_department" data-preview-id="inspection_department_preview">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <h3 style="margin: 0 0 8px 0; font-size: 16px; font-weight: 600;">اختر ملفات PDF أو اسحبها هنا</h3>
                        <p style="margin: 0; color: #6b7280; font-size: 14px;">حد أقصى 10 ميجابايت لكل ملف</p>
                    </div>
                    <div id="inspection_department_preview" class="file-preview"></div>
                </div>
            </div>
        </div>

        <!-- License Sections -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-certificate" style="color: #7c3aed;"></i>
                    أقسام التراخيص
                </h2>
            </div>
            <div class="section-content">
                <!-- Main Licenses Section Checkbox -->
                <div class="checkbox-group">
                    <input type="checkbox" id="licenses_section" name="licenses_section" class="checkbox-input">
                    <label for="licenses_section" class="checkbox-label">قسم التراخيص</label>
                </div>

                <!-- Conditional License Subsections -->
                <div id="license_subsections" style="display: none; margin-top: 24px;">
                    <!-- Fire Equipment Section -->
                    <div class="subsection" id="fire_equipment_subsection">
                        <div class="checkbox-group">
                            <input type="checkbox" id="fire_equipment_section" name="fire_equipment_section" class="checkbox-input">
                            <label for="fire_equipment_section" class="checkbox-label">قسم معدات مقاومة الحريق</label>
                        </div>
                        <div id="fire_equipment_files_section" style="display: none; margin-top: 16px;">
                            <div class="form-group">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <label class="form-label">ملفات قسم معدات مقاومة الحريق</label>
                                    <span id="fire_equipment_files_count" style="font-size: 12px; color: #6b7280;">0 ملف</span>
                                </div>
                                <input type="file" id="fire_equipment_files" name="fire_equipment_files" multiple accept=".pdf" style="display: none;">
                                <div class="upload-area" data-input-id="fire_equipment_files" data-preview-id="fire_equipment_files_preview">
                                    <div class="upload-icon">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                    </div>
                                    <h3 style="margin: 0 0 8px 0; font-size: 14px; font-weight: 600;">اختر ملفات PDF أو اسحبها هنا</h3>
                                    <p style="margin: 0; color: #6b7280; font-size: 12px;">حد أقصى 10 ميجابايت لكل ملف</p>
                                </div>
                                <div id="fire_equipment_files_preview" class="file-preview"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Commercial Records Section -->
                    <div class="border-l-4 border-green-200 pl-4">
                        <div class="form-group">
                            <div class="flex items-center">
                                <input type="checkbox" id="commercial_records_section" name="commercial_records_section" class="form-checkbox">
                                <label for="commercial_records_section" class="form-label-checkbox">قسم تراخيص السجلات التجارية</label>
                            </div>
                        </div>
                        <div id="commercial_records_files_section" class="hidden mt-4">
                            <div class="file-upload-section">
                                <div class="flex justify-between items-center mb-3">
                                    <span class="text-sm font-medium text-gray-900">ملفات قسم تراخيص السجلات التجارية</span>
                                    <span id="commercial_records_files_count" class="text-xs text-gray-500">0 ملف</span>
                                </div>
                                <input type="file" id="commercial_records_files" name="commercial_records_files" multiple accept=".pdf" class="hidden">
                                <div class="file-upload-area" data-input-id="commercial_records_files" data-preview-id="commercial_records_files_preview">
                                    <div class="text-center upload-content">
                                        <svg class="mx-auto h-8 w-8 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                        </svg>
                                        <div class="mt-2">
                                            <div class="text-sm text-gray-600 font-medium">اسحب وأفلت ملفات PDF هنا</div>
                                            <div class="text-sm text-blue-600 font-medium mt-1">أو اضغط لاختيار الملفات</div>
                                            <div class="text-xs text-gray-400 mt-2">ملفات PDF فقط - حد أقصى 10 ميجابايت لكل ملف</div>
                                        </div>
                                    </div>
                                </div>
                                <div id="commercial_records_files_preview" class="file-preview-area"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Engineering Offices Section -->
                    <div class="border-l-4 border-blue-200 pl-4">
                        <div class="form-group">
                            <div class="flex items-center">
                                <input type="checkbox" id="engineering_offices_section" name="engineering_offices_section" class="form-checkbox">
                                <label for="engineering_offices_section" class="form-label-checkbox">قسم تراخيص وتجديد المكاتب الهندسية والإستشارية</label>
                            </div>
                        </div>
                        <div id="engineering_offices_files_section" class="hidden mt-4">
                            <div class="file-upload-section">
                                <div class="flex justify-between items-center mb-3">
                                    <span class="text-sm font-medium text-gray-900">ملفات قسم تراخيص وتجديد المكاتب الهندسية والإستشارية</span>
                                    <span id="engineering_offices_files_count" class="text-xs text-gray-500">0 ملف</span>
                                </div>
                                <input type="file" id="engineering_offices_files" name="engineering_offices_files" multiple accept=".pdf" class="hidden">
                                <div class="file-upload-area" data-input-id="engineering_offices_files" data-preview-id="engineering_offices_files_preview">
                                    <div class="text-center upload-content">
                                        <svg class="mx-auto h-8 w-8 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                        </svg>
                                        <div class="mt-2">
                                            <div class="text-sm text-gray-600 font-medium">اسحب وأفلت ملفات PDF هنا</div>
                                            <div class="text-sm text-blue-600 font-medium mt-1">أو اضغط لاختيار الملفات</div>
                                            <div class="text-xs text-gray-400 mt-2">ملفات PDF فقط - حد أقصى 10 ميجابايت لكل ملف</div>
                                        </div>
                                    </div>
                                </div>
                                <div id="engineering_offices_files_preview" class="file-preview-area"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Hazardous Materials Section -->
                    <div class="border-l-4 border-yellow-200 pl-4">
                        <div class="form-group">
                            <div class="flex items-center">
                                <input type="checkbox" id="hazardous_materials_section" name="hazardous_materials_section" class="form-checkbox">
                                <label for="hazardous_materials_section" class="form-label-checkbox">قسم المواد الخطرة</label>
                            </div>
                        </div>
                        <div id="hazardous_materials_files_section" class="hidden mt-4">
                            <div class="file-upload-section">
                                <div class="flex justify-between items-center mb-3">
                                    <span class="text-sm font-medium text-gray-900">ملفات قسم المواد الخطرة</span>
                                    <span id="hazardous_materials_files_count" class="text-xs text-gray-500">0 ملف</span>
                                </div>
                                <input type="file" id="hazardous_materials_files" name="hazardous_materials_files" multiple accept=".pdf" class="hidden">
                                <div class="file-upload-area" data-input-id="hazardous_materials_files" data-preview-id="hazardous_materials_files_preview">
                                    <div class="text-center upload-content">
                                        <svg class="mx-auto h-8 w-8 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                        </svg>
                                        <div class="mt-2">
                                            <div class="text-sm text-gray-600 font-medium">اسحب وأفلت ملفات PDF هنا</div>
                                            <div class="text-sm text-blue-600 font-medium mt-1">أو اضغط لاختيار الملفات</div>
                                            <div class="text-xs text-gray-400 mt-2">ملفات PDF فقط - حد أقصى 10 ميجابايت لكل ملف</div>
                                        </div>
                                    </div>
                                </div>
                                <div id="hazardous_materials_files_preview" class="file-preview-area"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="section">
            <div class="section-content">
                <div style="display: flex; justify-content: space-between; align-items: center; gap: 16px;">
                    <a href="/dashboard" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </a>
                    <div style="display: flex; gap: 12px;">
                        <button type="button" onclick="showFileMappings()" class="btn btn-outline">
                            <i class="fas fa-list"></i>
                            عرض الملفات
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i>
                            إرسال الطلب
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
// Professional New Request Form JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing professional new request form...');

    // Main licenses section checkbox handler
    const licensesCheckbox = document.getElementById('licenses_section');
    const licenseSubsections = document.getElementById('license_subsections');

    if (licensesCheckbox && licenseSubsections) {
        licensesCheckbox.addEventListener('change', function() {
            if (this.checked) {
                licenseSubsections.style.display = 'block';
            } else {
                licenseSubsections.style.display = 'none';
                // Uncheck all subsections when main section is unchecked
                const subsectionCheckboxes = licenseSubsections.querySelectorAll('input[type="checkbox"]');
                subsectionCheckboxes.forEach(checkbox => {
                    checkbox.checked = false;
                    checkbox.dispatchEvent(new Event('change'));
                });
            }
        });
    }

    // Individual license subsection handlers
    const subsectionHandlers = [
        {
            checkboxId: 'fire_equipment_section',
            filesSectionId: 'fire_equipment_files_section'
        },
        {
            checkboxId: 'commercial_records_section',
            filesSectionId: 'commercial_records_files_section'
        },
        {
            checkboxId: 'engineering_offices_section',
            filesSectionId: 'engineering_offices_files_section'
        },
        {
            checkboxId: 'hazardous_materials_section',
            filesSectionId: 'hazardous_materials_files_section'
        }
    ];

    subsectionHandlers.forEach(handler => {
        const checkbox = document.getElementById(handler.checkboxId);
        const filesSection = document.getElementById(handler.filesSectionId);

        if (checkbox && filesSection) {
            checkbox.addEventListener('change', function() {
                if (this.checked) {
                    filesSection.style.display = 'block';
                    const subsection = filesSection.closest('.subsection');
                    if (subsection) subsection.classList.add('active');
                } else {
                    filesSection.style.display = 'none';
                    const subsection = filesSection.closest('.subsection');
                    if (subsection) subsection.classList.remove('active');
                    // Clear any selected files when unchecked
                    const fileInput = filesSection.querySelector('input[type="file"]');
                    if (fileInput) {
                        fileInput.value = '';
                        // Update file count
                        const countElement = document.getElementById(fileInput.id + '_count');
                        if (countElement) {
                            countElement.textContent = '0 ملف';
                        }
                        // Clear preview
                        const previewElement = document.getElementById(fileInput.id + '_preview');
                        if (previewElement) {
                            previewElement.innerHTML = '';
                        }
                    }
                }
            });
        }
    });

    // Professional file upload setup
    function setupFileUpload(inputId) {
        const fileInput = document.getElementById(inputId);
        const uploadArea = document.querySelector(`[data-input-id="${inputId}"]`);
        const previewArea = document.getElementById(inputId + '_preview');
        const countElement = document.getElementById(inputId + '_count');

        if (!fileInput || !uploadArea) {
            console.error('Missing required elements for:', inputId);
            return;
        }

        // Click handler for upload area
        uploadArea.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            fileInput.click();
        });

        // File input change handler
        fileInput.addEventListener('change', function(e) {
            updateFilePreview(this, previewArea, countElement);
        });

        // Drag and drop handlers
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            e.stopPropagation();
            this.style.borderColor = '#3b82f6';
            this.style.backgroundColor = '#eff6ff';
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            e.stopPropagation();
            this.style.borderColor = '#d1d5db';
            this.style.backgroundColor = '#f9fafb';
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            e.stopPropagation();
            this.style.borderColor = '#d1d5db';
            this.style.backgroundColor = '#f9fafb';

            const files = Array.from(e.dataTransfer.files);
            if (files.length > 0) {
                const dt = new DataTransfer();
                files.forEach(file => dt.items.add(file));
                fileInput.files = dt.files;
                updateFilePreview(fileInput, previewArea, countElement);
            }
        });
    }

    // Initialize all file upload sections
    const fileUploadSections = [
        'architectural_plans',
        'electrical_mechanical_plans',
        'inspection_department',
        'fire_equipment_files',
        'commercial_records_files',
        'engineering_offices_files',
        'hazardous_materials_files'
    ];

    // Setup each file upload section
    fileUploadSections.forEach(setupFileUpload);

    console.log('File upload system initialized for', fileUploadSections.length, 'sections');

    function updateFilePreview(input, previewArea, countElement) {
        if (!previewArea || !countElement) return;

        const files = Array.from(input.files);
        countElement.textContent = `${files.length} ملف`;

        previewArea.innerHTML = '';

        if (files.length > 0) {
            files.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';

                fileItem.innerHTML = `
                    <div class="file-info">
                        <div class="file-icon">
                            <i class="fas fa-file-pdf"></i>
                        </div>
                        <div class="file-details">
                            <div class="file-name">${file.name}</div>
                            <div class="file-size">${(file.size / 1024 / 1024).toFixed(2)} ميجابايت</div>
                        </div>
                    </div>
                    <button type="button" onclick="removeFile('${input.id}', ${index})" class="btn btn-outline" style="padding: 4px 8px; font-size: 12px;">
                        <i class="fas fa-times"></i>
                    </button>
                `;

                previewArea.appendChild(fileItem);
            });
        }
    }

    // Initialize all file upload sections
    const fileUploadSections = [
        'architectural_plans',
        'electrical_mechanical_plans',
        'inspection_department',
        'fire_equipment_files',
        'commercial_records_files',
        'engineering_offices_files',
        'hazardous_materials_files'
    ];

    fileUploadSections.forEach(setupFileUpload);

    // Global function to remove files
    window.removeFile = function(inputId, fileIndex) {
        const input = document.getElementById(inputId);
        const previewArea = document.getElementById(inputId + '_preview');
        const countElement = document.getElementById(inputId + '_count');

        if (input && input.files) {
            const dt = new DataTransfer();
            Array.from(input.files).forEach((file, index) => {
                if (index !== fileIndex) {
                    dt.items.add(file);
                }
            });
            input.files = dt.files;
            updateFilePreview(input, previewArea, countElement);
        }
    };

    // Global function to show file mappings
    window.showFileMappings = function() {
        let mappings = 'ملفات محددة:\n\n';
        let hasFiles = false;

        fileUploadSections.forEach(sectionId => {
            const input = document.getElementById(sectionId);
            if (input && input.files && input.files.length > 0) {
                hasFiles = true;
                mappings += `${sectionId}:\n`;
                Array.from(input.files).forEach((file, index) => {
                    mappings += `  ${index + 1}. ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)\n`;
                });
                mappings += '\n';
            }
        });

        if (!hasFiles) {
            mappings += 'لم يتم اختيار أي ملفات.';
        }

        alert(mappings);
    };

    console.log('Professional new request form initialized successfully');
});
</script>
{% endblock %}