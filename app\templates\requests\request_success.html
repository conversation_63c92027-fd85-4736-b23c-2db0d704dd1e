{% extends "base.html" %}

{% block title %}تم إنشاء الطلب بنجاح - CMSVS{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto space-y-6">
    <!-- Success Header -->
    <div class="card">
        <div class="card-header bg-green-50">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="w-8 h-8 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="mr-3">
                    <h4 class="text-2xl font-bold text-green-800">تم إنشاء الطلب بنجاح!</h4>
                    <p class="text-green-600 mt-1">سيتم مراجعة طلبك من قبل الإدارة وستحصل على إشعار بحالة الطلب</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Message -->
    {% if success %}
    <div class="card">
        <div class="card-body">
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                {{ success }}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Upload Warnings -->
    {% if upload_warnings %}
    <div class="card">
        <div class="card-body">
            <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
                <h6 class="font-semibold mb-2">تحذيرات رفع الملفات:</h6>
                <ul class="list-disc list-inside space-y-1">
                    {% for warning in upload_warnings %}
                    <li>{{ warning }}</li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Upload Errors -->
    {% if upload_errors %}
    <div class="card">
        <div class="card-body">
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                <h6 class="font-semibold mb-2">أخطاء رفع الملفات:</h6>
                <ul class="list-disc list-inside space-y-1">
                    {% for error in upload_errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Request Details -->
    <div class="card">
        <div class="card-header">
            <h5 class="text-lg font-semibold text-gray-900">تفاصيل الطلب</h5>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <div>
                        <label class="text-sm font-medium text-gray-500">رقم الطلب</label>
                        <div class="mt-1">
                            <code class="bg-gray-100 px-2 py-1 rounded text-sm font-mono">{{ new_request.request_number }}</code>
                        </div>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-500">الرمز التعريفي</label>
                        <div class="mt-1">
                            <code class="bg-blue-100 px-2 py-1 rounded text-sm font-mono text-blue-800">{{ new_request.unique_code }}</code>
                        </div>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-500">الاسم الكامل</label>
                        <div class="mt-1 text-sm text-gray-900">{{ new_request.full_name or 'غير محدد' }}</div>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-500">الرقم الشخصي</label>
                        <div class="mt-1 text-sm text-gray-900">{{ new_request.personal_number or 'غير محدد' }}</div>
                    </div>
                </div>
                <div class="space-y-4">
                    <div>
                        <label class="text-sm font-medium text-gray-500">الحالة</label>
                        <div class="mt-1">
                            <span class="badge-warning">قيد المراجعة</span>
                        </div>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-500">تاريخ الإنشاء</label>
                        <div class="mt-1 text-sm text-gray-900">{{ new_request.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                    </div>
                    {% if new_request.building_permit_number %}
                    <div>
                        <label class="text-sm font-medium text-gray-500">رقم إجازة البناء</label>
                        <div class="mt-1">
                            <code class="bg-green-100 px-2 py-1 rounded text-sm font-mono text-green-800">{{ new_request.building_permit_number }}</code>
                        </div>
                    </div>
                    {% endif %}
                    {% if new_request.phone_number %}
                    <div>
                        <label class="text-sm font-medium text-gray-500">رقم الهاتف</label>
                        <div class="mt-1 text-sm text-gray-900">{{ new_request.phone_number }}</div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Files Section -->
    <div class="card">
        <div class="card-header">
            <h5 class="text-lg font-semibold text-gray-900">المرفقات ({{ new_request.files|length }})</h5>
        </div>
        <div class="card-body">
            {% if new_request.files %}
            <div class="space-y-3">
                {% for file in new_request.files %}
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center flex-1 min-w-0">
                        <svg class="w-5 h-5 text-gray-400 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                        </svg>
                        <div class="flex-1 min-w-0">
                            <!-- Show the automated stored filename -->
                            <div class="text-sm font-medium text-gray-900 truncate">{{ file.stored_filename }}</div>
                            <!-- Show original filename as subtitle if different -->
                            {% if file.stored_filename != file.original_filename %}
                            <div class="text-xs text-gray-500 truncate">الاسم الأصلي: {{ file.original_filename }}</div>
                            {% endif %}
                            <!-- Show category -->
                            <div class="text-xs text-gray-500">{{ file.file_category or 'غير مصنف' }}</div>
                        </div>
                    </div>
                    <div class="text-sm text-gray-500 flex-shrink-0 ml-3">
                        {% if file.file_size and file.file_size > 0 %}
                            {% set size_mb = (file.file_size / 1024 / 1024) | round(2) %}
                            {{ size_mb }} MB
                            <div class="text-xs text-gray-400">({{ file.file_size }} bytes)</div>
                        {% elif file.file_size == 0 %}
                            <span class="text-yellow-600">0 MB (0 bytes)</span>
                        {% else %}
                            <span class="text-red-500">حجم غير محدد (file_size: {{ file.file_size }})</span>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="text-center py-6">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">لا توجد مرفقات</h3>
                <p class="mt-1 text-sm text-gray-500">لم يتم رفع أي ملفات مع هذا الطلب</p>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Important Tips -->
    <div class="card">
        <div class="card-header">
            <h5 class="text-lg font-semibold text-gray-900">نصائح مهمة</h5>
        </div>
        <div class="card-body">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <ul class="space-y-2 text-sm text-blue-800">
                    <li class="flex items-start">
                        <svg class="w-4 h-4 text-blue-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        احتفظ برقم الطلب <code class="bg-blue-100 px-1 rounded">{{ new_request.request_number }}</code> للمراجعة المستقبلية
                    </li>
                    <li class="flex items-start">
                        <svg class="w-4 h-4 text-blue-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        يمكنك متابعة حالة الطلب من لوحة التحكم الخاصة بك
                    </li>
                    <li class="flex items-start">
                        <svg class="w-4 h-4 text-blue-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h8V9H4v2z"></path>
                        </svg>
                        ستحصل على إشعار عند تغيير حالة الطلب
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="card">
        <div class="card-body">
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/dashboard" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                    </svg>
                    العودة للرئيسية
                </a>
                <a href="/requests/{{ new_request.id }}" class="btn-secondary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    عرض الطلب
                </a>
                <a href="/requests/new" class="btn-secondary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    طلب جديد
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}