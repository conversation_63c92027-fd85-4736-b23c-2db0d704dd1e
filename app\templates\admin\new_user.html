{% extends "base.html" %}

{% block title %}إنشاء مستخدم جديد - CMSVS{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">إنشاء مستخدم جديد</h1>
            <p class="text-gray-600 mt-2">قم بإدخال بيانات المستخدم الجديد</p>
        </div>
        <div>
            <a href="/admin/users"
               class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-arrow-right ml-2"></i>
                العودة لإدارة المستخدمين
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">
                    <i class="fas fa-user-plus text-green-600 ml-2"></i>
                    بيانات المستخدم الجديد
                </h2>

                <form method="post" action="/admin/users/new" id="newUserForm" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                                اسم المستخدم <span class="text-red-500">*</span>
                            </label>
                            <input type="text"
                                   id="username"
                                   name="username"
                                   value="{{ form_data.username if form_data else '' }}"
                                   required
                                   pattern="[a-zA-Z0-9_]{3,20}"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                            <p class="text-xs text-gray-500 mt-1">3-20 حرف، يمكن استخدام الأرقام والشرطة السفلية</p>
                        </div>
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                البريد الإلكتروني <span class="text-red-500">*</span>
                            </label>
                            <input type="email"
                                   id="email"
                                   name="email"
                                   value="{{ form_data.email if form_data else '' }}"
                                   required
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="full_name" class="block text-sm font-medium text-gray-700 mb-2">
                                الاسم الكامل <span class="text-red-500">*</span>
                            </label>
                            <input type="text"
                                   id="full_name"
                                   name="full_name"
                                   value="{{ form_data.full_name if form_data else '' }}"
                                   required
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                        </div>
                        <div>
                            <label for="role" class="block text-sm font-medium text-gray-700 mb-2">
                                الدور <span class="text-red-500">*</span>
                            </label>
                            <select id="role"
                                    name="role"
                                    required
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                                <option value="">اختر الدور</option>
                                {% for role_option in roles %}
                                <option value="{{ role_option }}" {% if form_data and form_data.role == role_option %}selected{% endif %}>
                                    {% if role_option == 'USER' %}مستخدم عادي
                                    {% elif role_option == 'ADMIN' %}مدير النظام
                                    {% endif %}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="text-xs text-gray-500 mt-1">
                                <strong>مستخدم عادي:</strong> يمكنه إنشاء وعرض طلباته فقط<br>
                                <strong>مدير النظام:</strong> يمكنه إدارة جميع المستخدمين والطلبات
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                                كلمة المرور <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <input type="password"
                                       id="password"
                                       name="password"
                                       required
                                       minlength="6"
                                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                                <button type="button"
                                        id="togglePassword"
                                        class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">يجب أن تكون كلمة المرور 6 أحرف على الأقل</p>
                        </div>
                        <div>
                            <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-2">
                                تأكيد كلمة المرور <span class="text-red-500">*</span>
                            </label>
                            <input type="password"
                                   id="confirm_password"
                                   name="confirm_password"
                                   required
                                   minlength="6"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                            <div id="passwordMismatch" class="text-red-500 text-xs mt-1 hidden">
                                كلمات المرور غير متطابقة
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-between items-center pt-6 border-t border-gray-200">
                        <a href="/admin/users"
                           class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg transition-colors">
                            <i class="fas fa-times ml-2"></i>
                            إلغاء
                        </a>
                        <button type="submit"
                                id="submitBtn"
                                class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors">
                            <i class="fas fa-user-plus ml-2"></i>
                            إنشاء المستخدم
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- User Roles Info -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-info-circle text-blue-600 ml-2"></i>
                    أدوار المستخدمين
                </h3>
                <div class="space-y-4">
                    <div class="border border-blue-200 rounded-lg p-4 bg-blue-50">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-user text-blue-600 ml-2"></i>
                            <h4 class="font-medium text-blue-900">مستخدم عادي</h4>
                        </div>
                        <ul class="text-sm text-blue-800 space-y-1">
                            <li>• إنشاء طلبات جديدة</li>
                            <li>• عرض طلباته الشخصية</li>
                            <li>• تعديل طلباته قبل المراجعة</li>
                            <li>• رفع وإدارة الملفات</li>
                        </ul>
                    </div>

                    <div class="border border-purple-200 rounded-lg p-4 bg-purple-50">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-crown text-purple-600 ml-2"></i>
                            <h4 class="font-medium text-purple-900">مدير النظام</h4>
                        </div>
                        <ul class="text-sm text-purple-800 space-y-1">
                            <li>• جميع صلاحيات المستخدم العادي</li>
                            <li>• إدارة جميع المستخدمين</li>
                            <li>• عرض وإدارة جميع الطلبات</li>
                            <li>• تغيير حالة الطلبات</li>
                            <li>• الوصول للوحة الإدارة</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Security Guidelines -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-shield-alt text-green-600 ml-2"></i>
                    إرشادات الأمان
                </h3>
                <div class="space-y-3">
                    <div class="flex items-start space-x-2 space-x-reverse">
                        <i class="fas fa-check-circle text-green-500 mt-1"></i>
                        <div class="text-sm text-gray-700">
                            استخدم كلمة مرور قوية تحتوي على أحرف وأرقام
                        </div>
                    </div>
                    <div class="flex items-start space-x-2 space-x-reverse">
                        <i class="fas fa-check-circle text-green-500 mt-1"></i>
                        <div class="text-sm text-gray-700">
                            تأكد من صحة البريد الإلكتروني للمستخدم
                        </div>
                    </div>
                    <div class="flex items-start space-x-2 space-x-reverse">
                        <i class="fas fa-check-circle text-green-500 mt-1"></i>
                        <div class="text-sm text-gray-700">
                            اختر الدور المناسب حسب احتياجات المستخدم
                        </div>
                    </div>
                    <div class="flex items-start space-x-2 space-x-reverse">
                        <i class="fas fa-exclamation-triangle text-yellow-500 mt-1"></i>
                        <div class="text-sm text-gray-700">
                            صلاحيات المدير تشمل الوصول الكامل للنظام
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-chart-bar text-indigo-600 ml-2"></i>
                    إحصائيات سريعة
                </h3>
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">إجمالي المستخدمين:</span>
                        <span class="font-semibold text-gray-900">{{ total_users or 0 }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">المديرون:</span>
                        <span class="font-semibold text-purple-600">{{ admin_count or 0 }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">المستخدمون العاديون:</span>
                        <span class="font-semibold text-blue-600">{{ user_count or 0 }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">المستخدمون النشطون:</span>
                        <span class="font-semibold text-green-600">{{ active_users or 0 }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Password visibility toggle
document.getElementById('togglePassword').addEventListener('click', function() {
    const passwordField = document.getElementById('password');
    const icon = this.querySelector('i');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
});

// Password confirmation validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    const mismatchDiv = document.getElementById('passwordMismatch');

    if (confirmPassword && password !== confirmPassword) {
        mismatchDiv.classList.remove('hidden');
    } else {
        mismatchDiv.classList.add('hidden');
    }
});

// Form validation
document.getElementById('newUserForm').addEventListener('submit', function(e) {
    const username = document.getElementById('username').value;
    const email = document.getElementById('email').value;
    const fullName = document.getElementById('full_name').value;
    const role = document.getElementById('role').value;
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;

    // Check required fields
    if (!username || !email || !fullName || !role || !password || !confirmPassword) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
        return false;
    }

    // Username validation
    const usernamePattern = /^[a-zA-Z0-9_]{3,20}$/;
    if (!usernamePattern.test(username)) {
        e.preventDefault();
        alert('اسم المستخدم يجب أن يكون بين 3-20 حرف ويحتوي على أحرف وأرقام وشرطة سفلية فقط');
        return false;
    }

    // Password validation
    if (password.length < 6) {
        e.preventDefault();
        alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
        return false;
    }

    if (password !== confirmPassword) {
        e.preventDefault();
        alert('كلمات المرور غير متطابقة');
        return false;
    }
});

// Real-time username validation
document.getElementById('username').addEventListener('input', function() {
    const username = this.value;
    const usernamePattern = /^[a-zA-Z0-9_]{3,20}$/;

    if (username && !usernamePattern.test(username)) {
        this.classList.add('border-red-300');
        this.classList.remove('border-gray-300');
    } else {
        this.classList.remove('border-red-300');
        this.classList.add('border-gray-300');
    }
});
</script>
{% endblock %}