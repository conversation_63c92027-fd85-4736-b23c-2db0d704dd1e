{% extends "base.html" %}

{% block title %}إدارة الطلبات - CMSVS{% endblock %}

{% block content %}

<style>
/* Mobile-First Professional Admin Requests Styling */
.page-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 12px;
    background: #f8fafc;
    min-height: 100vh;
}

@media (min-width: 640px) {
    .page-container {
        padding: 16px;
    }
}

@media (min-width: 768px) {
    .page-container {
        padding: 24px;
    }
}

.page-header {
    text-align: right;
    padding: 16px;
    margin-bottom: 20px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

@media (min-width: 640px) {
    .page-header {
        padding: 24px;
        margin-bottom: 24px;
        border-radius: 12px;
    }
}

@media (min-width: 768px) {
    .page-header {
        padding: 32px 24px;
        margin-bottom: 32px;
    }
}

.page-title {
    font-size: 24px;
    font-weight: 700;
    margin: 0 0 6px 0;
    color: #1f2937;
}

@media (min-width: 640px) {
    .page-title {
        font-size: 28px;
        margin: 0 0 8px 0;
    }
}

@media (min-width: 768px) {
    .page-title {
        font-size: 32px;
    }
}

.page-subtitle {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
}

@media (min-width: 640px) {
    .page-subtitle {
        font-size: 16px;
    }
}

.btn {
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border: none;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    text-decoration: none;
    min-height: 44px;
    justify-content: center;
}

@media (min-width: 640px) {
    .btn {
        padding: 10px 16px;
        gap: 6px;
        min-height: auto;
    }
}

.btn-secondary {
    background: #6b7280;
    color: #ffffff;
}

.btn-secondary:hover {
    background: #4b5563;
}

.btn-outline {
    background: transparent;
    border: 1px solid #d1d5db;
    color: #374151;
}

.btn-outline:hover {
    background: #f9fafb;
}

/* Dropdown positioning fixes */
.table-dropdown {
    position: relative;
}

.table-dropdown .dropdown-menu {
    position: absolute !important;
    z-index: 9999 !important;
    min-width: 14rem;
    max-width: 16rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid #e5e7eb;
    right: 0;
    top: 100%;
    margin-top: 0.25rem;
}

/* Support for upward dropdown positioning */
.table-dropdown .dropdown-menu.mb-2 {
    margin-bottom: 0.5rem;
    margin-top: 0;
}

.table-dropdown .dropdown-menu.mt-2 {
    margin-top: 0.5rem;
    margin-bottom: 0;
}

/* Prevent horizontal scroll when dropdown is open but allow vertical overflow */
.table-container {
    position: relative;
    overflow-x: auto;
    overflow-y: visible;
}

/* Ensure table rows can contain positioned dropdowns */
.table tbody tr {
    position: relative;
}

/* Ensure table cells can contain positioned dropdowns */
.table tbody td {
    position: relative;
}

/* Ensure dropdown container has proper positioning context */
.table-dropdown {
    position: relative;
}

/* Ensure card body allows overflow for dropdowns */
.card-body {
    overflow: visible !important;
}

/* Ensure card container allows overflow for dropdowns */
.card {
    overflow: visible !important;
}

/* Ensure main content area allows overflow */
.main-content {
    overflow: visible !important;
}

/* Mobile-First Card Styles */
.card {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    margin-bottom: 16px;
    overflow: hidden;
}

@media (min-width: 640px) {
    .card {
        border-radius: 12px;
        margin-bottom: 20px;
    }
}

@media (min-width: 768px) {
    .card {
        margin-bottom: 24px;
    }
}

.card-header {
    padding: 12px 16px;
    border-bottom: 1px solid #e5e7eb;
    background-color: #f9fafb;
}

@media (min-width: 640px) {
    .card-header {
        padding: 16px 20px;
    }
}

@media (min-width: 768px) {
    .card-header {
        padding: 20px 24px;
    }
}

.card-body {
    padding: 12px 16px;
}

@media (min-width: 640px) {
    .card-body {
        padding: 16px 20px;
    }
}

@media (min-width: 768px) {
    .card-body {
        padding: 20px 24px;
    }
}

.card-footer {
    padding: 12px 16px;
    border-top: 1px solid #e5e7eb;
    background-color: #f9fafb;
}

@media (min-width: 640px) {
    .card-footer {
        padding: 16px 20px;
    }
}

@media (min-width: 768px) {
    .card-footer {
        padding: 20px 24px;
    }
}

/* Mobile-First Form Styles */
.form-group {
    margin-bottom: 16px;
}

@media (min-width: 640px) {
    .form-group {
        margin-bottom: 20px;
    }
}

.form-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
}

@media (min-width: 640px) {
    .form-label {
        font-size: 16px;
        margin-bottom: 8px;
    }
}

.form-input,
.form-select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    background-color: #ffffff;
    transition: border-color 0.2s, box-shadow 0.2s;
    min-height: 44px;
}

@media (min-width: 640px) {
    .form-input,
    .form-select {
        padding: 12px 16px;
        font-size: 16px;
        min-height: auto;
    }
}

.form-input:focus,
.form-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Mobile-First Table Styles */
.table-container {
    position: relative;
    overflow: visible;
}

.table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

@media (min-width: 768px) {
    .table {
        font-size: 16px;
    }
}

.table-header {
    background-color: #f9fafb;
}

.table-header-cell {
    padding: 8px 12px;
    text-align: right;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

@media (min-width: 640px) {
    .table-header-cell {
        padding: 12px 16px;
        font-size: 14px;
    }
}

@media (min-width: 768px) {
    .table-header-cell {
        padding: 16px;
    }
}

.table-body {
    background-color: #ffffff;
}

.table-cell {
    padding: 8px 12px;
    text-align: right;
    border-bottom: 1px solid #e5e7eb;
    vertical-align: top;
}

@media (min-width: 640px) {
    .table-cell {
        padding: 12px 16px;
    }
}

@media (min-width: 768px) {
    .table-cell {
        padding: 16px;
    }
}

/* Mobile Card View Styles */
.mobile-table-card {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

@media (min-width: 640px) {
    .mobile-table-card {
        padding: 20px;
        margin-bottom: 16px;
        border-radius: 12px;
    }
}

.mobile-title {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 4px;
}

@media (min-width: 640px) {
    .mobile-title {
        font-size: 18px;
        margin-bottom: 6px;
    }
}

.mobile-table-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 4px 0;
}

@media (min-width: 640px) {
    .mobile-table-row {
        padding: 6px 0;
    }
}

.mobile-table-label {
    font-size: 12px;
    font-weight: 500;
    color: #6b7280;
    flex-shrink: 0;
    width: 100px;
}

@media (min-width: 640px) {
    .mobile-table-label {
        font-size: 14px;
        width: 120px;
    }
}

.mobile-table-value {
    font-size: 14px;
    color: #1f2937;
    text-align: left;
    flex: 1;
}

@media (min-width: 640px) {
    .mobile-table-value {
        font-size: 16px;
    }
}

/* Mobile responsive dropdown */
.table-dropdown .dropdown-menu {
    min-width: 10rem;
    max-width: 12rem;
    font-size: 14px;
    position: fixed !important;
    z-index: 9999 !important;
}

@media (min-width: 640px) {
    .table-dropdown .dropdown-menu {
        min-width: 12rem;
        max-width: 14rem;
        font-size: 16px;
    }
}

@media (min-width: 768px) {
    .table-dropdown .dropdown-menu {
        min-width: 14rem;
        max-width: 16rem;
    }
}

/* Search section toggle animation */
#searchSectionContent {
    transition: all 0.3s ease-in-out;
}

#searchToggleIcon {
    transition: transform 0.2s ease-in-out;
}

/* Mobile-First Badge Styles */
.badge-warning,
.badge-info,
.badge-success,
.badge-danger,
.badge-gray {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
}

@media (min-width: 640px) {
    .badge-warning,
    .badge-info,
    .badge-success,
    .badge-danger,
    .badge-gray {
        padding: 6px 12px;
        font-size: 12px;
        border-radius: 16px;
    }
}

.badge-warning {
    background-color: #fef3c7;
    color: #92400e;
}

.badge-info {
    background-color: #dbeafe;
    color: #1e40af;
}

.badge-success {
    background-color: #d1fae5;
    color: #065f46;
}

.badge-danger {
    background-color: #fee2e2;
    color: #991b1b;
}

.badge-gray {
    background-color: #f3f4f6;
    color: #374151;
}

/* Quick info badges */
.filter-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Mobile-First Button Variants */
.btn-primary {
    background-color: #3b82f6;
    color: #ffffff;
    border: 1px solid #3b82f6;
}

.btn-primary:hover {
    background-color: #2563eb;
    border-color: #2563eb;
}

.btn-secondary {
    background-color: #6b7280;
    color: #ffffff;
    border: 1px solid #6b7280;
}

.btn-secondary:hover {
    background-color: #4b5563;
    border-color: #4b5563;
}

.btn-outline {
    background-color: transparent;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-outline:hover {
    background-color: #f9fafb;
    border-color: #9ca3af;
}

.btn-success {
    background-color: #10b981;
    color: #ffffff;
    border: 1px solid #10b981;
}

.btn-success:hover {
    background-color: #059669;
    border-color: #059669;
}

.btn-danger {
    background-color: #ef4444;
    color: #ffffff;
    border: 1px solid #ef4444;
}

.btn-danger:hover {
    background-color: #dc2626;
    border-color: #dc2626;
}
</style>

<div class="page-container">
    <!-- Page Header -->
    <header class="page-header">
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4 sm:gap-5">
            <div class="flex-1">
                <h1 class="page-title">إدارة الطلبات</h1>
                <p class="page-subtitle">إدارة ومراقبة جميع طلبات النظام</p>
            </div>
            <div class="flex flex-col sm:flex-row gap-2 sm:gap-3">
                <a href="/admin/dashboard" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i>
                    العودة للوحة التحكم
                </a>
                <a href="/requests/new" class="btn btn-outline">
                    <i class="fas fa-plus"></i>
                    إنشاء طلب جديد
                </a>
            </div>
        </div>
    </header>

    <!-- New Request Button -->
    <div class="flex justify-end mb-4">
        <a href="/requests/new" class="btn-primary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            إنشاء طلب جديد
        </a>
    </div>

    <!-- Search Section -->
    <div class="card">
        <div class="card-header">
            <div class="flex items-center justify-between">
                <h5 class="text-lg font-semibold text-gray-900">البحث والتصفية</h5>
                <button type="button" onclick="toggleSearchSection()" class="{% if current_search or current_status %}text-blue-600 hover:text-blue-700{% else %}text-gray-500 hover:text-gray-700{% endif %} focus:outline-none p-1 rounded-md hover:bg-gray-100" id="searchToggleBtn">
                    {% if current_search or current_status %}
                    <div class="flex items-center space-x-1 rtl:space-x-reverse">
                        <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                        <svg class="w-5 h-5 transform transition-transform duration-200" id="searchToggleIcon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                    {% else %}
                    <svg class="w-5 h-5 transform transition-transform duration-200" id="searchToggleIcon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                    {% endif %}
                </button>
            </div>
            <!-- Quick info when collapsed -->
            <div id="searchQuickInfo" class="mt-2 text-sm text-gray-600 hidden">
                {% if current_search or current_status %}
                <div class="flex flex-wrap gap-2">
                    {% if current_search %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        بحث: {{ current_search }}
                    </span>
                    {% endif %}
                    {% if current_status %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        الحالة:
                        {% if current_status == 'pending' %}قيد المراجعة
                        {% elif current_status == 'in_progress' %}قيد التنفيذ
                        {% elif current_status == 'completed' %}مكتملة
                        {% elif current_status == 'rejected' %}مرفوضة
                        {% endif %}
                    </span>
                    {% endif %}
                </div>
                {% endif %}
                <div class="mt-1">
                    <span class="font-medium">{{ total_requests }} طلب إجمالي</span>
                    {% if total_pages > 1 %}
                    - الصفحة {{ current_page }} من {{ total_pages }}
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="card-body" id="searchSectionContent">
            <form method="get" action="/admin/requests" id="searchForm">
                <!-- Hidden input to reset to page 1 when searching -->
                <input type="hidden" name="page" value="1">
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="form-group">
                        <label class="form-label">البحث في الطلبات</label>
                        <div class="relative">
                            <input type="text" name="search" value="{{ current_search or '' }}" class="form-input pl-10" placeholder="ابحث برقم الطلب أو اسم المستخدم...">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">تصفية بالحالة</label>
                        <select name="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            {% for status in statuses %}
                            <option value="{{ status }}" {% if current_status == status %}selected{% endif %}>
                                {% if status == 'pending' %}قيد المراجعة
                                {% elif status == 'in_progress' %}قيد التنفيذ
                                {% elif status == 'completed' %}مكتملة
                                {% elif status == 'rejected' %}مرفوضة
                                {% endif %}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">عدد النتائج</label>
                        <select name="per_page" class="form-select">
                            <option value="10" {% if per_page == 10 %}selected{% endif %}>10</option>
                            <option value="20" {% if per_page == 20 %}selected{% endif %}>20</option>
                            <option value="50" {% if per_page == 50 %}selected{% endif %}>50</option>
                            <option value="100" {% if per_page == 100 %}selected{% endif %}>100</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label hidden sm:block">&nbsp;</label>
                        <div class="flex flex-col sm:flex-row gap-2 sm:gap-3">
                            <button type="submit" class="btn btn-secondary w-full sm:w-auto">بحث</button>
                            <a href="/admin/requests" class="btn btn-outline w-full sm:w-auto">مسح</a>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    {% if current_search %}
                    <small class="text-gray-600">
                        نتائج البحث: "{{ current_search }}"<br>
                        <span class="font-medium">{{ total_requests }} نتيجة إجمالية</span>
                        {% if total_pages > 1 %}
                        - الصفحة {{ current_page }} من {{ total_pages }}
                        {% endif %}
                    </small>
                    {% else %}
                    <small class="text-gray-600">
                        <span class="font-medium">{{ total_requests }} طلب إجمالي</span>
                        {% if total_pages > 1 %}
                        - الصفحة {{ current_page }} من {{ total_pages }}
                        {% endif %}
                    </small>
                    {% endif %}
                </div>
            </form>
        </div>
    </div>

    <!-- Status Filter Tabs -->
    <div class="card">
        <div class="card-body">
            <div class="grid grid-cols-2 sm:grid-cols-3 lg:flex lg:flex-wrap gap-2">
                <a href="/admin/requests" class="btn btn-secondary {% if not current_status %}btn-primary{% endif %} text-center">
                    الكل
                </a>
                {% for status in statuses %}
                <a href="/admin/requests?status={{ status }}" class="btn btn-secondary {% if current_status == status %}btn-primary{% endif %} text-center">
                    {% if status == 'pending' %}قيد المراجعة
                    {% elif status == 'in_progress' %}قيد التنفيذ
                    {% elif status == 'completed' %}مكتملة
                    {% elif status == 'rejected' %}مرفوضة
                    {% endif %}
                </a>
                {% endfor %}
                <a href="/admin/archive" class="btn btn-secondary text-center col-span-2 sm:col-span-1">
                    الطلبات المؤرشفة
                </a>
            </div>
        </div>
    </div>

    {% if requests %}

    <!-- Requests Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="text-lg font-semibold text-gray-900">قائمة الطلبات</h5>
        </div>
        <div class="card-body p-0">
            <!-- Desktop Table View -->
            <div class="hidden md:block table-container overflow-x-auto relative">
                <table class="table">
                    <thead class="table-header">
                        <tr>
                            <th class="table-header-cell">رقم الطلب</th>
                            <th class="table-header-cell">الرمز التعريفي</th>
                            <th class="table-header-cell">إجازة البناء</th>
                            <th class="table-header-cell">الإسم الثلاثي</th>
                            <th class="table-header-cell">الحالة</th>
                            <th class="table-header-cell">المرفقات</th>
                            <th class="table-header-cell">تاريخ الإنشاء</th>
                            <th class="table-header-cell">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="table-body">
                        {% for req in requests %}
                        <tr>
                            <td class="table-cell">
                                <code class="text-xs bg-gray-100 px-2 py-1 rounded">{{ req.request_number }}</code>
                            </td>
                            <td class="table-cell">
                                <div class="flex items-center space-x-2 rtl:space-x-reverse">
                                    <code class="text-xs bg-blue-100 px-2 py-1 rounded text-blue-800">{{ req.unique_code }}</code>
                                    <button onclick="copyToClipboard('{{ req.unique_code }}')" class="text-gray-400 hover:text-gray-600" title="نسخ الرمز">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                            <td class="table-cell">
                                {% if req.building_permit_number %}
                                <code class="text-xs bg-green-100 px-2 py-1 rounded text-green-800">{{ req.building_permit_number }}</code>
                                {% else %}
                                <span class="text-gray-400 text-sm">غير محدد</span>
                                {% endif %}
                            </td>
                            <td class="table-cell">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center text-xs font-medium ml-3">
                                        {{ req.full_name[0] if req.full_name else 'م' }}
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ req.full_name or 'غير محدد' }}</div>
                                        <div class="text-xs text-gray-500">{{ req.user.email }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="table-cell">
                                {% if req.status.value == 'pending' %}
                                <span class="badge-warning">قيد المراجعة</span>
                                {% elif req.status.value == 'in_progress' %}
                                <span class="badge-info">قيد التنفيذ</span>
                                {% elif req.status.value == 'completed' %}
                                <span class="badge-success">مكتمل</span>
                                {% elif req.status.value == 'rejected' %}
                                <span class="badge-danger">مرفوض</span>
                                {% endif %}
                            </td>
                            <td class="table-cell">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 text-gray-400 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                    </svg>
                                    <span class="text-sm text-gray-600">{{ req.files|length }}</span>
                                </div>
                            </td>
                            <td class="table-cell">
                                <div class="text-sm text-gray-900">{{ req.created_at.strftime('%Y-%m-%d') }}</div>
                                <div class="text-xs text-gray-500">{{ req.created_at.strftime('%H:%M') }}</div>
                            </td>
                            <td class="table-cell">
                                <div class="flex space-x-2 rtl:space-x-reverse">
                                    <a href="/requests/{{ req.id }}" class="text-primary-600 hover:text-primary-500 text-sm">
                                        عرض
                                    </a>
                                    <div class="table-dropdown relative inline-block text-left">
                                        <button type="button" class="text-gray-400 hover:text-gray-600" onclick="toggleDropdown({{ req.id }}, event)">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
                                            </svg>
                                        </button>
                                        <div id="dropdown-{{ req.id }}" class="dropdown-menu hidden w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                                            <div class="py-1">
                                                <div class="px-4 py-2 text-xs text-gray-500 border-b">إجراءات الطلب</div>

                                                <!-- Edit Request Option -->
                                                <a href="/requests/{{ req.id }}/edit" class="block w-full text-right px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">
                                                    <i class="fas fa-edit text-blue-600 ml-2"></i>
                                                    تعديل الطلب
                                                </a>

                                                <!-- Divider -->
                                                <div class="border-t border-gray-100 my-1"></div>

                                                <!-- Status Change Options -->
                                                <div class="px-4 py-2 text-xs text-gray-500">تغيير الحالة</div>
                                                <form method="post" action="/admin/requests/{{ req.id }}/update-status" class="block">
                                                    <input type="hidden" name="status" value="pending">
                                                    <button type="submit" class="block w-full text-right px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                        قيد المراجعة
                                                    </button>
                                                </form>
                                                <form method="post" action="/admin/requests/{{ req.id }}/update-status" class="block">
                                                    <input type="hidden" name="status" value="in_progress">
                                                    <button type="submit" class="block w-full text-right px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                        قيد التنفيذ
                                                    </button>
                                                </form>
                                                <form method="post" action="/admin/requests/{{ req.id }}/update-status" class="block">
                                                    <input type="hidden" name="status" value="completed">
                                                    <button type="submit" class="block w-full text-right px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                        مكتمل
                                                    </button>
                                                </form>
                                                <form method="post" action="/admin/requests/{{ req.id }}/update-status" class="block">
                                                    <input type="hidden" name="status" value="rejected">
                                                    <button type="submit" class="block w-full text-right px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                        مرفوض
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Mobile Card View -->
            <div class="md:hidden">
                {% for req in requests %}
                <div class="mobile-table-card border-r-4 border-r-{{ 'green' if req.status.value == 'approved' else 'yellow' if req.status.value == 'pending' else 'red' if req.status.value == 'rejected' else 'blue' }}-500">
                    <!-- Request Header -->
                    <div class="flex justify-between items-start mb-3">
                        <div>
                            <div class="mobile-title">{{ req.full_name or 'غير محدد' }}</div>
                            <code class="text-xs bg-gray-100 px-2 py-1 rounded">{{ req.request_number }}</code>
                        </div>
                        <div class="text-right">
                            {% if req.status.value == 'pending' %}
                            <span class="badge-warning">قيد المراجعة</span>
                            {% elif req.status.value == 'approved' %}
                            <span class="badge-success">مقبول</span>
                            {% elif req.status.value == 'rejected' %}
                            <span class="badge-danger">مرفوض</span>
                            {% elif req.status.value == 'in_progress' %}
                            <span class="badge-info">قيد التنفيذ</span>
                            {% else %}
                            <span class="badge-gray">{{ req.status.value }}</span>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Request Details -->
                    <div class="space-y-2 mb-4">
                        <div class="mobile-table-row">
                            <span class="mobile-table-label">المستخدم:</span>
                            <span class="mobile-table-value">{{ req.user.full_name }}</span>
                        </div>

                        <div class="mobile-table-row">
                            <span class="mobile-table-label">الرمز التعريفي:</span>
                            <span class="mobile-table-value">{{ req.unique_code or 'غير محدد' }}</span>
                        </div>

                        <div class="mobile-table-row">
                            <span class="mobile-table-label">إجازة البناء:</span>
                            <span class="mobile-table-value">{{ req.building_permit or 'غير محدد' }}</span>
                        </div>

                        <div class="mobile-table-row">
                            <span class="mobile-table-label">المرفقات:</span>
                            <span class="mobile-table-value">
                                {% set file_count = req.files|length %}
                                {% if file_count > 0 %}
                                <span class="text-blue-600">{{ file_count }} ملف</span>
                                {% else %}
                                <span class="text-gray-500">لا توجد مرفقات</span>
                                {% endif %}
                            </span>
                        </div>

                        <div class="mobile-table-row">
                            <span class="mobile-table-label">تاريخ الإنشاء:</span>
                            <span class="mobile-table-value">{{ req.created_at.strftime('%Y-%m-%d') }}</span>
                        </div>
                    </div>

                    <!-- Admin Action Buttons -->
                    <div class="flex flex-wrap gap-2">
                        <a href="/admin/requests/{{ req.id }}" class="btn-primary text-xs px-3 py-2">
                            <i class="fas fa-eye"></i>
                            عرض
                        </a>

                        <a href="/admin/requests/{{ req.id }}/edit" class="btn-secondary text-xs px-3 py-2">
                            <i class="fas fa-edit"></i>
                            تعديل
                        </a>

                        {% if req.files %}
                        <a href="/admin/requests/{{ req.id }}/files" class="btn-outline text-xs px-3 py-2">
                            <i class="fas fa-paperclip"></i>
                            المرفقات
                        </a>
                        {% endif %}

                        <!-- Status Update Buttons -->
                        {% if req.status.value == 'pending' %}
                        <button onclick="updateStatus({{ req.id }}, 'approved')" class="btn-success text-xs px-3 py-2">
                            <i class="fas fa-check"></i>
                            قبول
                        </button>
                        <button onclick="updateStatus({{ req.id }}, 'rejected')" class="btn-danger text-xs px-3 py-2">
                            <i class="fas fa-times"></i>
                            رفض
                        </button>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Pagination Controls -->
        {% if total_pages > 1 %}
        <div class="card-footer">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div class="text-sm text-gray-700 text-center sm:text-right">
                    عرض {{ ((current_page - 1) * per_page) + 1 }} إلى {{ ((current_page - 1) * per_page) + requests|length }} من {{ total_requests }} نتيجة
                </div>
                <div class="flex items-center justify-center sm:justify-end space-x-2 rtl:space-x-reverse overflow-x-auto">
                    <!-- Previous Page -->
                    {% if current_page > 1 %}
                    <a href="?page={{ current_page - 1 }}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_search %}&search={{ current_search }}{% endif %}&per_page={{ per_page }}"
                       class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                        السابق
                    </a>
                    {% else %}
                    <span class="px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed">
                        السابق
                    </span>
                    {% endif %}

                    <!-- Page Numbers -->
                    <div class="flex items-center space-x-1 rtl:space-x-reverse">
                        {% set start_page = [1, current_page - 2]|max %}
                        {% set end_page = [total_pages, current_page + 2]|min %}

                        {% if start_page > 1 %}
                        <a href="?page=1{% if current_status %}&status={{ current_status }}{% endif %}{% if current_search %}&search={{ current_search }}{% endif %}&per_page={{ per_page }}"
                           class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">1</a>
                        {% if start_page > 2 %}
                        <span class="px-2 py-2 text-sm text-gray-500">...</span>
                        {% endif %}
                        {% endif %}

                        {% for page_num in range(start_page, end_page + 1) %}
                        {% if page_num == current_page %}
                        <span class="px-3 py-2 text-sm font-medium text-white bg-primary-600 border border-primary-600 rounded-md">
                            {{ page_num }}
                        </span>
                        {% else %}
                        <a href="?page={{ page_num }}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_search %}&search={{ current_search }}{% endif %}&per_page={{ per_page }}"
                           class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            {{ page_num }}
                        </a>
                        {% endif %}
                        {% endfor %}

                        {% if end_page < total_pages %}
                        {% if end_page < total_pages - 1 %}
                        <span class="px-2 py-2 text-sm text-gray-500">...</span>
                        {% endif %}
                        <a href="?page={{ total_pages }}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_search %}&search={{ current_search }}{% endif %}&per_page={{ per_page }}"
                           class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">{{ total_pages }}</a>
                        {% endif %}
                    </div>

                    <!-- Next Page -->
                    {% if current_page < total_pages %}
                    <a href="?page={{ current_page + 1 }}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_search %}&search={{ current_search }}{% endif %}&per_page={{ per_page }}"
                       class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                        التالي
                    </a>
                    {% else %}
                    <span class="px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed">
                        التالي
                    </span>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    {% else %}
    <!-- Empty State -->
    <div class="card">
        <div class="card-body text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <h4 class="mt-4 text-lg font-medium text-gray-900">لا توجد طلبات</h4>
            {% if current_status %}
            <p class="mt-2 text-sm text-gray-600">لا توجد طلبات بحالة "{{ current_status }}"</p>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>

<script>
// Toggle search section
function toggleSearchSection() {
    const content = document.getElementById('searchSectionContent');
    const quickInfo = document.getElementById('searchQuickInfo');
    const icon = document.getElementById('searchToggleIcon');

    if (content.style.display === 'none') {
        // Show search section
        content.style.display = 'block';
        quickInfo.classList.add('hidden');
        icon.style.transform = 'rotate(0deg)';
        localStorage.setItem('searchSectionExpanded', 'true');
    } else {
        // Hide search section
        content.style.display = 'none';
        quickInfo.classList.remove('hidden');
        icon.style.transform = 'rotate(-90deg)';
        localStorage.setItem('searchSectionExpanded', 'false');
    }
}

// Initialize search section state on page load
document.addEventListener('DOMContentLoaded', function() {
    const content = document.getElementById('searchSectionContent');
    const quickInfo = document.getElementById('searchQuickInfo');
    const icon = document.getElementById('searchToggleIcon');

    // Check if there are active filters - if so, keep expanded
    const hasActiveFilters = {{ 'true' if current_search or current_status else 'false' }};

    // Get saved state from localStorage, default to collapsed if no active filters
    const isExpanded = hasActiveFilters || localStorage.getItem('searchSectionExpanded') === 'true';

    if (!isExpanded) {
        content.style.display = 'none';
        quickInfo.classList.remove('hidden');
        icon.style.transform = 'rotate(-90deg)';
    } else {
        content.style.display = 'block';
        quickInfo.classList.add('hidden');
        icon.style.transform = 'rotate(0deg)';
    }
});

function toggleDropdown(requestId, event) {
    // Prevent default behavior and stop propagation
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }

    const dropdown = document.getElementById(`dropdown-${requestId}`);
    const isHidden = dropdown.classList.contains('hidden');

    // Close all other dropdowns first
    document.querySelectorAll('[id^="dropdown-"]').forEach(d => {
        if (d.id !== `dropdown-${requestId}`) {
            d.classList.add('hidden');
        }
    });

    if (isHidden) {
        // Position dropdown intelligently
        positionDropdown(dropdown, event.target);
        dropdown.classList.remove('hidden');
    } else {
        dropdown.classList.add('hidden');
    }

    // Prevent page scrolling
    return false;
}

function positionDropdown(dropdown, button) {
    // Reset any custom positioning
    dropdown.style.left = '';
    dropdown.style.right = '';
    dropdown.style.top = '';
    dropdown.style.bottom = '';

    // Get container and viewport dimensions
    const container = dropdown.closest('.table-container');
    const buttonRect = button.getBoundingClientRect();
    const containerRect = container.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Check if dropdown would go off-screen to the right
    const dropdownWidth = 224; // 14rem = 224px
    const spaceOnRight = viewportWidth - buttonRect.right;

    if (spaceOnRight < dropdownWidth) {
        // Not enough space on right, position to the left
        dropdown.style.right = '0';
        dropdown.style.left = 'auto';
    }

    // Check if dropdown would go off-screen at the bottom
    const dropdownHeight = 200; // Estimated height
    const spaceBelow = viewportHeight - buttonRect.bottom;

    if (spaceBelow < dropdownHeight) {
        // Not enough space below, position above
        dropdown.style.top = 'auto';
        dropdown.style.bottom = '100%';
        dropdown.style.marginTop = '0';
        dropdown.style.marginBottom = '0.25rem';
    }
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // You could add a toast notification here
        console.log('Copied to clipboard: ' + text);
    });
}

// Close dropdowns when clicking outside
document.addEventListener('click', function(event) {
    const dropdowns = document.querySelectorAll('[id^="dropdown-"]');
    const clickedButton = event.target.closest('button[onclick*="toggleDropdown"]');

    dropdowns.forEach(dropdown => {
        // Don't close if clicking on the dropdown itself or its toggle button
        if (!dropdown.contains(event.target) && !clickedButton) {
            dropdown.classList.add('hidden');
        }
    });
});

// Close dropdown when scrolling
document.addEventListener('scroll', function() {
    document.querySelectorAll('[id^="dropdown-"]').forEach(dropdown => {
        dropdown.classList.add('hidden');
    });
}, true);

// Close dropdown when window is resized
window.addEventListener('resize', function() {
    document.querySelectorAll('[id^="dropdown-"]').forEach(dropdown => {
        dropdown.classList.add('hidden');
    });
});

// Prevent scrolling when pressing space or enter on dropdown buttons
document.addEventListener('keydown', function(event) {
    const target = event.target;
    if (target.tagName === 'BUTTON' && target.onclick && target.onclick.toString().includes('toggleDropdown')) {
        if (event.key === ' ' || event.key === 'Enter') {
            event.preventDefault();
            target.click();
        }
    }
});
</script>
{% endblock %}