{% extends "base.html" %}

{% block title %}إنجازات المستخدمين - CMSVS{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Page Header -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex justify-between items-start">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 mb-2">إنجازات المستخدمين</h1>
                <p class="text-gray-600">تتبع وعرض إنجازات وتقدم جميع المستخدمين في النظام</p>
            </div>
            <div class="flex items-center gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">{{ total_users or 0 }}</div>
                    <div class="text-sm text-gray-500">مستخدم</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">{{ total_achievements or 0 }}</div>
                    <div class="text-sm text-gray-500">إنجاز</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Controls -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
            <div class="flex flex-wrap gap-2">
                <button class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    الكل ({{ total_users or 0 }})
                </button>
                <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                    المتفوقون ({{ high_achievers_count or 0 }})
                </button>
                <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                    النشطون ({{ active_users_count or 0 }})
                </button>
                <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                    الجدد ({{ new_users_count or 0 }})
                </button>
            </div>
            <div class="flex items-center gap-3">
                <div class="relative">
                    <i class="fas fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    <input type="text" placeholder="البحث في الإنجازات..."
                           class="pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div class="flex bg-gray-100 rounded-lg p-1">
                    <button data-view="grid" class="px-3 py-1 bg-white rounded shadow-sm text-gray-700">
                        <i class="fas fa-th"></i>
                    </button>
                    <button data-view="list" class="px-3 py-1 text-gray-500 hover:text-gray-700">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Achievement Cards Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="achievementsGrid">
        {% for user_achievement in user_achievements %}
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
             data-user-id="{{ user_achievement.user_id }}"
             data-category="{{ user_achievement.category }}">

            <!-- Card Header -->
            <div class="flex justify-between items-start mb-4">
                <div class="flex items-center space-x-3 space-x-reverse">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                        <span class="text-blue-600 font-semibold">
                            {{ user_achievement.initials or user_achievement.full_name[:2] }}
                        </span>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-900">{{ user_achievement.full_name }}</h3>
                        <p class="text-sm text-gray-500">@{{ user_achievement.username }}</p>
                    </div>
                </div>
                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                    {{ user_achievement.status_text or 'نشط' }}
                </span>
            </div>

            <!-- Key Stats Row -->
            <div class="grid grid-cols-3 gap-4 mb-4">
                <div class="text-center">
                    <div class="text-lg font-bold text-gray-900">{{ user_achievement.total_requests or 0 }}</div>
                    <div class="text-xs text-gray-500">إجمالي</div>
                </div>
                <div class="text-center">
                    <div class="text-lg font-bold text-green-600">{{ user_achievement.completed_requests or 0 }}</div>
                    <div class="text-xs text-gray-500">مكتملة</div>
                </div>
                <div class="text-center">
                    <div class="text-lg font-bold text-blue-600">{{ user_achievement.achievement_score or 0 }}%</div>
                    <div class="text-xs text-gray-500">الإنجاز</div>
                </div>
            </div>

            <!-- Progress Metrics -->
            <div class="space-y-3 mb-4">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">يومي</span>
                    <span class="text-sm font-medium">
                        {{ user_achievement.daily.completed or 0 }}/{{ user_achievement.daily.goal or 0 }}
                    </span>
                    <span class="text-sm text-blue-600">{{ user_achievement.daily.percentage or 0 }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-blue-600 h-2 rounded-full" style="width: {{ user_achievement.daily.percentage or 0 }}%"></div>
                </div>

                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">أسبوعي</span>
                    <span class="text-sm font-medium">
                        {{ user_achievement.weekly.completed or 0 }}/{{ user_achievement.weekly.goal or 0 }}
                    </span>
                    <span class="text-sm text-green-600">{{ user_achievement.weekly.percentage or 0 }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-green-600 h-2 rounded-full" style="width: {{ user_achievement.weekly.percentage or 0 }}%"></div>
                </div>

                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">شهري</span>
                    <span class="text-sm font-medium">
                        {{ user_achievement.monthly.completed or 0 }}/{{ user_achievement.monthly.goal or 0 }}
                    </span>
                    <span class="text-sm text-purple-600">{{ user_achievement.monthly.percentage or 0 }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-purple-600 h-2 rounded-full" style="width: {{ user_achievement.monthly.percentage or 0 }}%"></div>
                </div>
            </div>

            <!-- Badges & Actions -->
            <div class="flex justify-between items-center pt-4 border-t border-gray-200">
                <div class="flex space-x-1 space-x-reverse">
                    {% for badge in user_achievement.badges %}
                        <span class="w-6 h-6 bg-yellow-100 text-yellow-600 rounded-full flex items-center justify-center text-xs">
                            <i class="fas fa-medal"></i>
                        </span>
                    {% endfor %}
                    {% if user_achievement.badges|length == 0 %}
                        <span class="text-xs text-gray-400">لا توجد شارات</span>
                    {% endif %}
                </div>
                <div class="flex space-x-2 space-x-reverse">
                    <button data-user-id="{{ user_achievement.user_id }}"
                            class="text-blue-600 hover:text-blue-800 transition-colors">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button data-user-id="{{ user_achievement.user_id }}"
                            class="text-green-600 hover:text-green-800 transition-colors">
                        <i class="fas fa-chart-line"></i>
                    </button>
                </div>
            </div>

            <!-- Footer -->
            <div class="mt-3 pt-3 border-t border-gray-200">
                <div class="text-xs text-gray-500 text-center">
                    آخر نشاط: {{ user_achievement.last_activity or 'غير محدد' }}
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Empty State -->
    {% if user_achievements|length == 0 %}
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
        <i class="fas fa-trophy text-gray-400 text-4xl mb-4"></i>
        <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد إنجازات للعرض</h3>
        <p class="text-gray-500">لم يتم العثور على أي مستخدمين يطابقون المعايير المحددة.</p>
    </div>
    {% endif %}

    <!-- Pagination -->
    {% if total_pages > 1 %}
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <nav aria-label="صفحات الإنجازات" class="flex justify-center">
            <ul class="flex items-center space-x-2 space-x-reverse">
                {% if current_page > 1 %}
                <li>
                    <a href="?page={{ current_page - 1 }}"
                       class="px-3 py-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                        السابق
                    </a>
                </li>
                {% endif %}

                {% for page_num in range(1, total_pages + 1) %}
                <li>
                    <a href="?page={{ page_num }}"
                       class="px-3 py-2 {{ 'bg-blue-600 text-white' if page_num == current_page else 'text-gray-500 hover:text-gray-700 hover:bg-gray-100' }} rounded-lg transition-colors">
                        {{ page_num }}
                    </a>
                </li>
                {% endfor %}

                {% if current_page < total_pages %}
                <li>
                    <a href="?page={{ current_page + 1 }}"
                       class="px-3 py-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                        التالي
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
    </div>
    {% endif %}
</div>

<!-- User Details Modal -->
<div id="userDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50" tabindex="-1" aria-labelledby="userDetailsModalLabel" aria-hidden="true">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto">
            <div class="flex justify-between items-center p-6 border-b border-gray-200">
                <h2 id="userDetailsModalLabel" class="text-xl font-semibold text-gray-900">تفاصيل إنجازات المستخدم</h2>
                <button type="button" onclick="closeModal()" class="text-gray-400 hover:text-gray-600 transition-colors" aria-label="إغلاق">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="userDetailsContent" class="p-6">
                <!-- Content will be loaded dynamically -->
                <div class="text-center py-8">
                    <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-3"></i>
                    <p class="text-gray-500">جاري تحميل البيانات...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// View toggle functionality
document.querySelectorAll('[data-view]').forEach(button => {
    button.addEventListener('click', function() {
        const view = this.dataset.view;
        const grid = document.getElementById('achievementsGrid');

        // Update button states
        document.querySelectorAll('[data-view]').forEach(btn => {
            btn.classList.remove('bg-white', 'shadow-sm');
            btn.classList.add('text-gray-500');
        });
        this.classList.add('bg-white', 'shadow-sm');
        this.classList.remove('text-gray-500');

        // Update grid layout
        if (view === 'list') {
            grid.className = 'space-y-4';
            grid.querySelectorAll('.bg-white').forEach(card => {
                card.className = 'bg-white rounded-xl shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow flex items-center justify-between';
            });
        } else {
            grid.className = 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6';
            grid.querySelectorAll('.bg-white').forEach(card => {
                card.className = 'bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow';
            });
        }
    });
});

// Modal functionality
function openModal(userId) {
    const modal = document.getElementById('userDetailsModal');
    const content = document.getElementById('userDetailsContent');

    modal.classList.remove('hidden');

    // Load user details (you can implement AJAX call here)
    content.innerHTML = `
        <div class="text-center py-8">
            <i class="fas fa-user-circle text-gray-400 text-4xl mb-3"></i>
            <p class="text-gray-500">تفاصيل المستخدم ${userId}</p>
        </div>
    `;
}

function closeModal() {
    document.getElementById('userDetailsModal').classList.add('hidden');
}

// Event listeners for action buttons
document.addEventListener('click', function(e) {
    if (e.target.closest('[data-user-id]')) {
        const userId = e.target.closest('[data-user-id]').dataset.userId;
        if (e.target.classList.contains('fa-eye')) {
            openModal(userId);
        } else if (e.target.classList.contains('fa-chart-line')) {
            // Handle chart view
            console.log('Show chart for user:', userId);
        }
    }
});

// Close modal when clicking outside
document.getElementById('userDetailsModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});

// Search functionality
document.querySelector('input[placeholder="البحث في الإنجازات..."]').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const cards = document.querySelectorAll('[data-user-id]');

    cards.forEach(card => {
        const userName = card.querySelector('h3').textContent.toLowerCase();
        const userHandle = card.querySelector('p').textContent.toLowerCase();

        if (userName.includes(searchTerm) || userHandle.includes(searchTerm)) {
            card.style.display = '';
        } else {
            card.style.display = 'none';
        }
    });
});
</script>
{% endblock %}