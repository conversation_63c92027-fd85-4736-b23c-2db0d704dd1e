{% extends "base.html" %}

{% block title %}تقرير نشاط المستخدمين - لوحة التحكم{% endblock %}

{% block content %}
<style>
/* Professional User Activity Report Styling */
.page-container {
    max-width: 1600px;
    margin: 0 auto;
    padding: 24px;
    background: #f8fafc;
    min-height: 100vh;
}

.page-header {
    text-align: center;
    padding: 32px 24px;
    margin-bottom: 32px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.page-title {
    font-size: 32px;
    font-weight: 700;
    margin: 0 0 8px 0;
    color: #1f2937;
}

.page-subtitle {
    font-size: 16px;
    color: #6b7280;
    margin: 0;
}

.section {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;
    overflow: hidden;
}

.section-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #374151;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-content {
    padding: 24px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.stat-card {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: #6b7280;
    font-weight: 500;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-label {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
}

.form-control {
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    background: #ffffff;
    transition: border-color 0.2s;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-primary {
    background: #3b82f6;
    color: white;
}

.btn-primary:hover {
    background: #2563eb;
}

.btn-secondary {
    background: #6b7280;
    color: white;
}

.btn-secondary:hover {
    background: #4b5563;
}

.btn-success {
    background: #10b981;
    color: white;
}

.btn-success:hover {
    background: #059669;
}

.table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.table th,
.table td {
    padding: 12px;
    text-align: right;
    border-bottom: 1px solid #e5e7eb;
}

.table th {
    background: #f9fafb;
    font-weight: 600;
    color: #374151;
}

.table tbody tr:hover {
    background: #f9fafb;
}

.activity-level {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.activity-level.high {
    background: #dcfce7;
    color: #166534;
}

.activity-level.medium {
    background: #dbeafe;
    color: #1e40af;
}

.activity-level.low {
    background: #fef3c7;
    color: #92400e;
}

.activity-level.inactive {
    background: #f3f4f6;
    color: #374151;
}

.monthly-breakdown {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 8px;
    margin-top: 8px;
}

.month-stat {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 8px;
    text-align: center;
    font-size: 12px;
}

.month-stat .month-name {
    font-weight: 600;
    color: #374151;
    margin-bottom: 2px;
}

.month-stat .month-count {
    color: #3b82f6;
    font-weight: 700;
}

@media (max-width: 768px) {
    .page-container {
        padding: 16px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .table {
        font-size: 12px;
    }
    
    .table th,
    .table td {
        padding: 8px;
    }

    /* Responsive Charts */
    .section-content > div[style*="grid-template-columns: 1fr 1fr"] {
        grid-template-columns: 1fr !important;
    }
}

/* Chart Container Styling */
.chart-container {
    position: relative;
    background: #ffffff;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.chart-title {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 16px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}
</style>

<div class="page-container">
    <!-- Page Header -->
    <header class="page-header">
        <div style="display: flex; justify-content: space-between; align-items: flex-start; gap: 20px;">
            <div style="flex: 1;">
                <h1 class="page-title">تقرير نشاط المستخدمين</h1>
                <p class="page-subtitle">تقرير شامل لنشاط جميع المستخدمين خلال فترة {{ selected_period }} شهر</p>
            </div>
            <div style="display: flex; gap: 12px; flex-wrap: wrap;">
                <a href="/admin/requests-records" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i>
                    العودة لسجل الطلبات
                </a>
                <a href="/admin/dashboard" class="btn btn-secondary">
                    <i class="fas fa-home"></i>
                    لوحة التحكم
                </a>
            </div>
        </div>
    </header>

    <!-- Report Controls -->
    <div class="section">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-filter" style="color: #3b82f6;"></i>
                إعدادات التقرير
            </h2>
        </div>
        <div class="section-content">
            <form method="get" action="/admin/user-activity-report" style="display: flex; gap: 16px; align-items: end; flex-wrap: wrap;">
                <div class="form-group" style="min-width: 200px;">
                    <label for="period" class="form-label">فترة التقرير</label>
                    <select id="period" name="period" class="form-control">
                        {% for option in period_options %}
                        <option value="{{ option.value }}" {% if selected_period == option.value %}selected{% endif %}>
                            {{ option.label }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div style="display: flex; gap: 12px;">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-chart-bar"></i>
                        إنشاء التقرير
                    </button>
                    <a href="/admin/user-activity-report?period={{ selected_period }}&format=csv" class="btn btn-success">
                        <i class="fas fa-download"></i>
                        تحميل CSV
                    </a>
                    <a href="/admin/user-activity-report?period={{ selected_period }}&format=pdf" class="btn btn-secondary">
                        <i class="fas fa-file-pdf"></i>
                        تحميل PDF
                    </a>
                    <a href="/admin/user-activity-report?period={{ selected_period }}&format=arabic" class="btn btn-info" target="_blank">
                        <i class="fas fa-language"></i>
                        تقرير عربي للطباعة
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Report Summary -->
    {% if report_data %}
    <div class="section">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-chart-pie" style="color: #10b981;"></i>
                ملخص التقرير
            </h2>
        </div>
        <div class="section-content">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" style="color: #3b82f6;">{{ report_data.total_users }}</div>
                    <div class="stat-label">إجمالي المستخدمين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" style="color: #10b981;">{{ report_data.active_users }}</div>
                    <div class="stat-label">المستخدمين النشطين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" style="color: #f59e0b;">{{ report_data.inactive_users }}</div>
                    <div class="stat-label">المستخدمين غير النشطين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" style="color: #8b5cf6;">{{ report_data.total_requests }}</div>
                    <div class="stat-label">إجمالي الطلبات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" style="color: #ef4444;">{{ report_data.average_requests_per_user }}</div>
                    <div class="stat-label">متوسط الطلبات لكل مستخدم</div>
                </div>
            </div>
            <div style="text-align: center; color: #6b7280; font-size: 14px; margin-top: 16px;">
                <i class="fas fa-calendar"></i>
                فترة التقرير:
                {% if report_data.period_start_datetime %}{{ report_data.period_start_datetime.strftime('%Y-%m-%d') }}{% else %}غير محدد{% endif %}
                إلى
                {% if report_data.period_end_datetime %}{{ report_data.period_end_datetime.strftime('%Y-%m-%d') }}{% else %}غير محدد{% endif %}
                <br>
                <i class="fas fa-clock" style="margin-right: 16px;"></i>
                تم إنشاء التقرير:
                {% if report_data.generated_at_datetime %}{{ report_data.generated_at_datetime.strftime('%Y-%m-%d %H:%M:%S') }}{% else %}غير محدد{% endif %}
            </div>
        </div>
    </div>

    <!-- Data Visualizations Section -->
    <div class="section">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-chart-line" style="color: #3b82f6;"></i>
                التصورات البيانية والإحصائيات المرئية
            </h2>
        </div>
        <div class="section-content">
            <!-- Charts Grid -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 32px;">
                <!-- Activity Distribution Pie Chart -->
                <div style="background: #f9fafb; border-radius: 12px; padding: 24px;">
                    <h3 style="font-size: 18px; font-weight: 600; color: #1f2937; margin: 0 0 16px 0; text-align: center;">
                        <i class="fas fa-chart-pie" style="color: #10b981; margin-left: 8px;"></i>
                        توزيع مستويات النشاط
                    </h3>
                    <div style="position: relative; height: 300px;">
                        <canvas id="activityDistributionChart"></canvas>
                    </div>
                </div>

                <!-- Requests Trend Line Chart -->
                <div style="background: #f9fafb; border-radius: 12px; padding: 24px;">
                    <h3 style="font-size: 18px; font-weight: 600; color: #1f2937; margin: 0 0 16px 0; text-align: center;">
                        <i class="fas fa-chart-line" style="color: #3b82f6; margin-left: 8px;"></i>
                        اتجاه الطلبات الشهرية
                    </h3>
                    <div style="position: relative; height: 300px;">
                        <canvas id="requestsTrendChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Top Users Bar Chart -->
            <div style="background: #f9fafb; border-radius: 12px; padding: 24px; margin-bottom: 24px;">
                <h3 style="font-size: 18px; font-weight: 600; color: #1f2937; margin: 0 0 16px 0; text-align: center;">
                    <i class="fas fa-chart-bar" style="color: #8b5cf6; margin-left: 8px;"></i>
                    أكثر المستخدمين نشاطاً (أعلى 10 مستخدمين)
                </h3>
                <div style="position: relative; height: 400px;">
                    <canvas id="topUsersChart"></canvas>
                </div>
            </div>

            <!-- Activity Heatmap -->
            <div style="background: #f9fafb; border-radius: 12px; padding: 24px;">
                <h3 style="font-size: 18px; font-weight: 600; color: #1f2937; margin: 0 0 16px 0; text-align: center;">
                    <i class="fas fa-calendar-alt" style="color: #f59e0b; margin-left: 8px;"></i>
                    خريطة النشاط الشهرية
                </h3>
                <div style="position: relative; height: 300px;">
                    <canvas id="activityHeatmapChart"></canvas>
                </div>
                <div style="text-align: center; margin-top: 12px; font-size: 12px; color: #6b7280;">
                    <i class="fas fa-info-circle"></i>
                    كلما كان اللون أغمق، كان النشاط أعلى في ذلك الشهر
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed User Reports -->
    <div class="section">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-users" style="color: #8b5cf6;"></i>
                تفاصيل نشاط المستخدمين
            </h2>
        </div>
        <div class="section-content">
            {% if report_data.user_reports_with_datetime %}
            <div style="overflow-x: auto;">
                <table class="table">
                    <thead>
                        <tr>
                            <th>المستخدم</th>
                            <th>إجمالي الطلبات</th>
                            <th>متوسط يومي</th>
                            <th>متوسط أسبوعي</th>
                            <th>متوسط شهري</th>
                            <th>الطلبات الأخيرة</th>
                            <th>مستوى النشاط</th>
                            <th>التفاصيل الشهرية</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in report_data.user_reports_with_datetime %}
                        <tr>
                            <td>
                                <div style="font-weight: 600; color: #374151;">{{ user.name }}</div>
                                <div style="font-size: 12px; color: #6b7280;">{{ user.email }}</div>
                            </td>
                            <td>
                                <span style="font-weight: 600; color: #3b82f6;">{{ user.total_requests }}</span>
                            </td>
                            <td>{{ user.daily_average }}</td>
                            <td>{{ user.weekly_average }}</td>
                            <td>{{ user.monthly_average }}</td>
                            <td>{{ user.recent_requests }}</td>
                            <td>
                                <span class="activity-level
                                    {% if user.activity_level == 'نشط جداً' %}high
                                    {% elif user.activity_level == 'نشط' %}medium
                                    {% elif user.activity_level == 'نشط أحياناً' %}low
                                    {% else %}inactive{% endif %}">
                                    {{ user.activity_level }}
                                </span>
                            </td>
                            <td>
                                <button onclick="toggleMonthlyDetails('{{ user.user_id }}')" class="btn" style="padding: 4px 8px; font-size: 12px; background: #f3f4f6; color: #374151;">
                                    <i class="fas fa-chart-line"></i>
                                    عرض التفاصيل
                                </button>
                                <div id="monthly-{{ user.user_id }}" style="display: none;" class="monthly-breakdown">
                                    {% for month in user.monthly_requests %}
                                    <div class="month-stat">
                                        <div class="month-name">{{ month.month }}</div>
                                        <div class="month-count">{{ month.count }}</div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div style="text-align: center; padding: 40px; color: #6b7280;">
                <i class="fas fa-chart-bar" style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;"></i>
                <h3 style="margin: 0 0 8px 0;">لا توجد بيانات</h3>
                <p style="margin: 0;">لم يتم العثور على بيانات نشاط للفترة المحددة</p>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('User activity report page loaded successfully');

    // Auto-submit form when period changes
    const periodSelect = document.getElementById('period');
    if (periodSelect) {
        periodSelect.addEventListener('change', function() {
            this.form.submit();
        });
    }
});

// Toggle monthly details for a user
function toggleMonthlyDetails(userId) {
    const detailsDiv = document.getElementById('monthly-' + userId);
    const button = event.target.closest('button');
    const icon = button.querySelector('i');

    if (detailsDiv.style.display === 'none' || detailsDiv.style.display === '') {
        detailsDiv.style.display = 'grid';
        icon.className = 'fas fa-chart-line-down';
        button.innerHTML = '<i class="fas fa-chart-line-down"></i> إخفاء التفاصيل';
    } else {
        detailsDiv.style.display = 'none';
        icon.className = 'fas fa-chart-line';
        button.innerHTML = '<i class="fas fa-chart-line"></i> عرض التفاصيل';
    }
}

// Chart.js Visualizations
{% if report_data and report_data.user_reports_with_datetime %}
document.addEventListener('DOMContentLoaded', function() {
    // Activity Distribution Pie Chart
    const activityCtx = document.getElementById('activityDistributionChart').getContext('2d');
    const activityData = {
        {% set high_activity = report_data.user_reports_with_datetime|selectattr("activity_level", "equalto", "نشط جداً")|list|length %}
        {% set medium_activity = report_data.user_reports_with_datetime|selectattr("activity_level", "equalto", "نشط")|list|length %}
        {% set low_activity = report_data.user_reports_with_datetime|selectattr("activity_level", "equalto", "نشط أحياناً")|list|length %}
        {% set inactive = report_data.user_reports_with_datetime|selectattr("activity_level", "equalto", "غير نشط")|list|length %}

        labels: ['نشط جداً', 'نشط', 'نشط أحياناً', 'غير نشط'],
        datasets: [{
            data: [{{ high_activity }}, {{ medium_activity }}, {{ low_activity }}, {{ inactive }}],
            backgroundColor: ['#10b981', '#3b82f6', '#f59e0b', '#ef4444'],
            borderWidth: 2,
            borderColor: '#ffffff'
        }]
    };

    new Chart(activityCtx, {
        type: 'pie',
        data: activityData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        font: { size: 12 },
                        padding: 15,
                        usePointStyle: true
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                        }
                    }
                }
            }
        }
    });

    // Requests Trend Line Chart
    const trendCtx = document.getElementById('requestsTrendChart').getContext('2d');
    const trendData = {};

    {% for user in report_data.user_reports_with_datetime %}
        {% for month in user.monthly_requests %}
            if (!trendData['{{ month.month }}']) {
                trendData['{{ month.month }}'] = 0;
            }
            trendData['{{ month.month }}'] += {{ month.count }};
        {% endfor %}
    {% endfor %}

    const trendMonths = Object.keys(trendData).sort();
    const trendValues = trendMonths.map(month => trendData[month]);

    new Chart(trendCtx, {
        type: 'line',
        data: {
            labels: trendMonths,
            datasets: [{
                label: 'إجمالي الطلبات',
                data: trendValues,
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#3b82f6',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    position: 'right',
                    title: { display: true, text: 'عدد الطلبات' },
                    grid: { color: 'rgba(0, 0, 0, 0.1)' }
                },
                x: {
                    title: { display: true, text: 'الشهر' },
                    grid: { display: false }
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                    labels: { font: { size: 12 } }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        title: function(context) {
                            return 'شهر: ' + context[0].label;
                        },
                        label: function(context) {
                            return 'إجمالي الطلبات: ' + context.parsed.y;
                        }
                    }
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            }
        }
    });

    // Top Users Bar Chart
    const topUsersCtx = document.getElementById('topUsersChart').getContext('2d');
    const sortedUsers = [
        {% set sorted_users = report_data.user_reports_with_datetime|sort(attribute='total_requests', reverse=true) %}
        {% for user in sorted_users %}
        {% if loop.index <= 10 %}
        {
            name: '{{ user.name|truncate(15, true, "...") }}',
            requests: {{ user.total_requests }},
            daily_avg: {{ user.daily_average }}
        }{% if not loop.last and loop.index < 10 %},{% endif %}
        {% endif %}
        {% endfor %}
    ];

    new Chart(topUsersCtx, {
        type: 'bar',
        data: {
            labels: sortedUsers.map(u => u.name),
            datasets: [{
                label: 'إجمالي الطلبات',
                data: sortedUsers.map(u => u.requests),
                backgroundColor: '#3b82f6',
                borderColor: '#1d4ed8',
                borderWidth: 1
            }, {
                label: 'متوسط يومي',
                data: sortedUsers.map(u => u.daily_avg),
                backgroundColor: '#10b981',
                borderColor: '#059669',
                borderWidth: 1,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    position: 'right',
                    title: { display: true, text: 'إجمالي الطلبات' }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: { display: true, text: 'متوسط يومي' },
                    grid: { drawOnChartArea: false }
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                    labels: { font: { size: 12 } }
                }
            }
        }
    });

    // Activity Heatmap (Monthly breakdown)
    const heatmapCtx = document.getElementById('activityHeatmapChart').getContext('2d');
    const monthlyData = {};

    {% for user in report_data.user_reports_with_datetime %}
        {% for month in user.monthly_requests %}
            if (!monthlyData['{{ month.month }}']) {
                monthlyData['{{ month.month }}'] = 0;
            }
            monthlyData['{{ month.month }}'] += {{ month.count }};
        {% endfor %}
    {% endfor %}

    const months = Object.keys(monthlyData).sort();
    const values = months.map(month => monthlyData[month]);
    const maxValue = Math.max(...values);

    new Chart(heatmapCtx, {
        type: 'bar',
        data: {
            labels: months,
            datasets: [{
                label: 'الطلبات الشهرية',
                data: values,
                backgroundColor: values.map(val => {
                    const intensity = val / maxValue;
                    return `rgba(59, 130, 246, ${0.3 + intensity * 0.7})`;
                }),
                borderColor: '#3b82f6',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    position: 'right',
                    title: { display: true, text: 'عدد الطلبات' }
                }
            },
            plugins: {
                legend: { display: false },
                tooltip: {
                    callbacks: {
                        title: function(context) {
                            return 'شهر: ' + context[0].label;
                        },
                        label: function(context) {
                            return 'إجمالي الطلبات: ' + context.parsed.y;
                        }
                    }
                }
            }
        }
    });
});
{% endif %}
</script>

<!-- Chart.js Library -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

{% endblock %}
