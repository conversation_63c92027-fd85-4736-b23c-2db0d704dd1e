# CMSVS Local Development Environment Configuration
# This file is for LOCAL DEVELOPMENT with local PostgreSQL

# Environment
ENVIRONMENT=development
DEBUG=True

# Database Configuration (Local PostgreSQL)
DATABASE_URL=postgresql://cmsvs_user:cmsvs_password123@localhost:5432/cmsvs_dev
DB_PASSWORD=cmsvs_password123

# Database Pool Settings (Smaller for local development)
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=1800

# Security (Development - Less Strict)
SECRET_KEY=local-development-secret-key-change-this-for-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60  # Longer for development convenience

# File Upload Configuration
MAX_FILE_SIZE=52428800   # 50MB
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,jpg,jpeg,png,gif,xlsx,xls,ppt,pptx
UPLOAD_DIRECTORY=uploads

# Application Configuration
APP_NAME=CMSVS Internal System (Local Development)
APP_VERSION=1.0.0-dev

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123  # Simple password for development

# Network Configuration (Local)
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0
CORS_ORIGINS=http://localhost:3000,http://localhost:8000,http://127.0.0.1:8000

# SSL/TLS Configuration (Disabled for development)
FORCE_HTTPS=False
SECURE_COOKIES=False
HSTS_MAX_AGE=0

# Logging Configuration (More verbose for development)
LOG_LEVEL=DEBUG
LOG_FILE=logs/app.log
LOG_MAX_SIZE=10485760
LOG_BACKUP_COUNT=3
LOG_FORMAT=standard

# Rate Limiting (Disabled for development)
RATE_LIMIT_ENABLED=False
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=60
RATE_LIMIT_BURST=100

# Session Configuration
SESSION_TIMEOUT=3600  # 1 hour for development
REMEMBER_ME_DURATION=86400  # 1 day for development

# Performance Configuration
ENABLE_GZIP=True
STATIC_FILE_CACHE=0  # No caching in development
API_CACHE_TTL=0

# Monitoring (Enabled for development debugging)
HEALTH_CHECK_ENABLED=True
METRICS_ENABLED=True

# Security Headers (Disabled for development)
SECURITY_HEADERS_ENABLED=False
CONTENT_SECURITY_POLICY=default-src 'self' 'unsafe-inline' 'unsafe-eval' *
X_FRAME_OPTIONS=SAMEORIGIN
X_CONTENT_TYPE_OPTIONS=nosniff
REFERRER_POLICY=no-referrer-when-cross-origin
